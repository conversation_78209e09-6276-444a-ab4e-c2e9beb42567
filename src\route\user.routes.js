const express = require('express');
const { UserController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { APP_USER } = require('../constant/enums');

class UserRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.post('/signup', UserController.signUp);
        this.router.post('/login', UserController.login);
        this.router.post('/req-reset-password', UserController.requestResetPassword);
        this.router.post('/reset-password', UserController.resetPassword);
        this.router.get('/dropdown', Authentication.authUser, UserController.getDropdowns);
        this.router.get('/all-app-user', Authentication.authUser, PermissionMiddleware.checkPermission(APP_USER), UserController.getAllAppUsers);
        this.router.get('/app-user/:id', Authentication.authUser, PermissionMiddleware.checkPermission(APP_USER), UserController.getAppUserById);
        // this.router.get('/logout', Authentication.authUser, UserController.logout);
        // this.router.put('/update', Authentication.authUser, UserController.updateUser);
        // this.router.delete('/delete', Authentication.authUser, UserController.deleteUser);
        this.router.post('/create-user', Authentication.authUser, PermissionMiddleware.checkPermission(APP_USER), UserController.createUserSuperAdmin);
        this.router.put('/update-user/:userId', Authentication.authUser, PermissionMiddleware.checkPermission(APP_USER), UserController.updateUserByAdmin);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = UserRoute;
