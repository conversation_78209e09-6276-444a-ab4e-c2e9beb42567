const mongoose = require('mongoose');

const ghlPushSchema = new mongoose.Schema({
    ghl_id: { type: String, required: true },
    customer_id: { type: String, required: true },
    customerLocalId: { type: mongoose.Types.ObjectId, ref: 'highmasters', required: true },
    locationName: { type: String },
    locationLocalId: { type: mongoose.Types.ObjectId, ref: 'locations', required: true },
    annual_total: { type: Number, default: 0 },
    lifetime_total: { type: Number, default: 0 }
},
    {
        timestamps: true
    });

// Add indexes for better query performance
ghlPushSchema.index({ customer_id: 1 }); // Primary lookup field
ghlPushSchema.index({ customerLocalId: 1 }); // Customer reference
ghlPushSchema.index({ locationLocalId: 1 }); // Location reference
ghlPushSchema.index({ ghl_id: 1 }); // GHL ID lookup
ghlPushSchema.index({ createdAt: -1 }); // Date sorting

const GhlPush = mongoose.model('ghlmasters', ghlPushSchema);
module.exports = GhlPush;
