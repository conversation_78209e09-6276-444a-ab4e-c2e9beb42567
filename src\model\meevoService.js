const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const meevoServiceSchema = new Schema({
    service_id: { type: String, required: true, unique: true }, // Links to MeevoSale.cservice
    name: { type: String, required: true },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations', required: true }, // Ref to Location
}, {
    timestamps: true
});

const MeevoService = mongoose.model('meevoservice', meevoServiceSchema);

module.exports = MeevoService;