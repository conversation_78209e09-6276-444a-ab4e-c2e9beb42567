const { mongoose } = require("mongoose")

const getAllUsers = (params, userLocations) => [
    {
        $match: {

            ...(params.location_Id ? { locations: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
                locations: {
                    $in: userLocations
                }
            }
                : {}),
            ...(params.dateFrom && params.dateEnd
                ? {
                    createdAt: {
                        $gte: new Date(params.dateFrom),
                        $lte: new Date(params.dateEnd),
                    }
                }
                : params.dateFrom
                    ? {
                        createdAt: {
                            $gte: new Date(params.dateFrom),
                        }
                    }
                    : {})
        }
    },
    {
        $lookup: {
            from: "locations",
            localField: "locations",
            foreignField: "_id",
            as: "locationDetails"
        }
    },
    {
        $addFields: {
            name: {
                $concat: [
                    { $ifNull: ["$firstName", ""] },
                    " ",
                    { $ifNull: ["$lastName", ""] }
                ]
            },
            locationNames: {
                $map: {
                    input: "$locationDetails",
                    as: "loc",
                    in: "$$loc.location"
                }
            }
        }
    },
    {
        $project: {
            _id: 1,
            name: 1,
            email: 1,
            phone: 1,
            createdAt: 1,
            locations: '$locationNames',
            locationIds: '$locations'
        }
    }
];


const getAppUserByIdQuery = (id) => [
    {
        $match: {
            _id: new mongoose.Types.ObjectId(id),
            isDeleted: false
        }
    },
    {
        $lookup: {
            from: "permissions",
            let: {
                locations: "$locations",
                userId: "$_id"
            },
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [
                                { $eq: ["$userId", "$$userId"] },
                                { $eq: ["$isDeleted", false] },
                                {
                                    $in: [
                                        "$locationId",
                                        "$$locations"
                                    ]
                                }
                            ]
                        }
                    }
                }
            ],
            as: "permissions"
        }
    },
    {
        $addFields: {
            globalPermission: {
                $reduce: {
                    input: {
                        $filter: {
                            input: "$permissions",
                            as: "perm",
                            cond: {
                                $eq: ["$$perm.isGlobal", true]
                            }
                        }
                    },
                    initialValue: {},
                    in: {
                        $mergeObjects: [
                            "$$value",
                            "$$this.permissions"
                        ]
                    }
                }
            },
            customPermissions: {
                $map: {
                    input: {
                        $filter: {
                            input: "$permissions",
                            as: "perm",
                            cond: {
                                $eq: ["$$perm.isGlobal", false]
                            }
                        }
                    },
                    as: "cp",
                    in: {
                        locationId: "$$cp.locationId",
                        permissions: "$$cp.permissions"
                    }
                }
            }
        }
    },
    {
        $project: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            email: 1,
            phone: 1,
            isEnable: 1,
            locations: 1,
            description: 1,
            globalPermission: 1,
            customPermissions: 1
        }
    }
]
const userSearchFeilds = [
    "_id",
    "name",
    "email",
    "phone",
    "locations",
    "createdAt"
]

module.exports = {
    getAllUsers,
    userSearchFeilds,
    getAppUserByIdQuery
}