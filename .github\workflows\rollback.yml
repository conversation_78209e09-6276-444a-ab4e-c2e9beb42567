name: Rollback Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        default: 'tgp'
        type: choice
        options:
          - tgp
          - demo
          - wp
      backup_timestamp:
        description: 'Backup timestamp (optional - uses latest if not provided)'
        required: false
        type: string

jobs:
  rollback:
    runs-on: ubuntu-latest
    
    steps:
    - name: List available backups
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          echo "📋 Available backups:"
          ls -la ~/backups/ | grep better_csi_ms_ || echo "No backups found"

    - name: Perform rollback
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          set -e
          
          echo "🔄 Starting rollback for environment: ${{ github.event.inputs.environment }}"
          
          # Determine which backup to use
          if [ -n "${{ github.event.inputs.backup_timestamp }}" ]; then
            BACKUP_DIR="~/backups/better_csi_ms_${{ github.event.inputs.backup_timestamp }}"
            if [ ! -d "$BACKUP_DIR" ]; then
              echo "❌ Backup not found: $BACKUP_DIR"
              exit 1
            fi
          else
            BACKUP_DIR=$(ls -td ~/backups/better_csi_ms_* | head -n 1)
            if [ -z "$BACKUP_DIR" ]; then
              echo "❌ No backups found!"
              exit 1
            fi
          fi
          
          echo "📦 Using backup: $BACKUP_DIR"
          
          # Stop current service
          echo "⏹️ Stopping current service..."
          pm2 stop api-${{ github.event.inputs.environment }} || echo "Service not running"
          
          # Create a backup of current state before rollback
          CURRENT_BACKUP="~/backups/before_rollback_$(date +%Y%m%d_%H%M%S)"
          mkdir -p ~/backups
          cp -r ~/better_csi/api_service/Better_CSI_MS $CURRENT_BACKUP
          echo "📦 Current state backed up to: $CURRENT_BACKUP"
          
          # Restore from backup
          echo "🔄 Restoring from backup..."
          cd ~/better_csi/api_service/Better_CSI_MS
          rm -rf ./*
          cp -r $BACKUP_DIR/* .
          
          # Install dependencies (in case they changed)
          echo "📦 Installing dependencies..."
          npm install --production --silent
          
          # Start service
          echo "▶️ Starting service..."
          case "${{ github.event.inputs.environment }}" in
            "tgp")
              pm2 start npm --name api-tgp -- run tgp_start
              ;;
            "demo")
              pm2 start npm --name api-demo -- run demo_start
              ;;
            "wp")
              pm2 start npm --name api-wp -- run wp_start
              ;;
            *)
              echo "❌ Unknown environment: ${{ github.event.inputs.environment }}"
              exit 1
              ;;
          esac
          
          # Save PM2 configuration
          pm2 save
          
          echo "✅ Rollback completed!"

    - name: Verify rollback
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          echo "🔍 Verifying rollback..."
          
          # Wait for service to start
          sleep 10
          
          # Check if service is running
          if pm2 list | grep -q "api-${{ github.event.inputs.environment }}.*online"; then
            echo "✅ Rollback successful! Service api-${{ github.event.inputs.environment }} is running."
            pm2 show api-${{ github.event.inputs.environment }}
            
            echo "📋 Recent logs:"
            pm2 logs api-${{ github.event.inputs.environment }} --lines 10 --nostream
          else
            echo "❌ Rollback failed! Service api-${{ github.event.inputs.environment }} is not running."
            pm2 logs api-${{ github.event.inputs.environment }} --lines 50 --nostream
            exit 1
          fi
