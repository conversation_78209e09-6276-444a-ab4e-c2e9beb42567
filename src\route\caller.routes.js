const express = require('express');
const { CallerController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
class CallerRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.post('/create-history', Authentication.authUser, CallerController.createCallerHistory);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = CallerRoute;
