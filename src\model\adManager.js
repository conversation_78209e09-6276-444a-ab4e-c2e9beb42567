const mongoose = require('mongoose');
const { Schema } = mongoose;

const AdSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String },
  status: { type: String },
  campaign_id: { type: String },
  impressions: { type: String },
  image_url: { type: String },
  leads: { type: Number, default: 0 }
}, { _id: false });

const AdManagerSchema = new Schema({
  sourceId: {
    type: Schema.Types.ObjectId,
    ref: 'source',
    required: true
  },
  locationId: {
    type: Schema.Types.ObjectId,
    ref: 'location',
    required: true
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },
  notificationEmail: {
    type: String,
    lowercase: true,
    trim: true
  },
  password: {
    type: String
  },
  adId: {
    type: String
  },
  formId: {
    type: String
  },
  campaignId: {
    type: String
  },
  campaignGroupId: {
    type: String
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'appuser',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'appuser'
  },
  conversion: {
    type: Number,
    default: 0
  },
  views: {
    type: Number,
    default: 0
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  ads: [AdSchema] // 👈 added ads array here
}, {
  timestamps: true
});

const AdManager = mongoose.model('admanager', AdManagerSchema);
module.exports = AdManager;
