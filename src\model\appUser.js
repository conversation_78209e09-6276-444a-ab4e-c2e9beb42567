const mongoose = require('mongoose');

const appUserSchema = new mongoose.Schema({
    firstName: {
        type: String,
        required: true
    },
    lastName: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true
    },
    phone: {
        type: String
    },
    locations: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'locations'
    }],
    description: {
        type: String
    },
    isEnable: {
        type: Boolean,
        default: false,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    resetPasswordToken: {
        type: String
    },
    resetPasswordExpires: {
        type: String
    },
    conciergeSpecialist: { type: Boolean, default: false }
}, {
    timestamps: true
});

const AppUser = mongoose.model('appuser', appUserSchema);
module.exports = AppUser;
