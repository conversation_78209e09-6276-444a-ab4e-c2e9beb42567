const mongoose = require('mongoose');
const { StringSchema } = require('yup');

const daySchema = new mongoose.Schema({
    openTime: { type: String },
    closeTime: { type: String },
}, { _id: false });

const weekDetailsSchema = new mongoose.Schema({
    monday: { type: daySchema },
    tuesday: { type: daySchema },
    wednesday: { type: daySchema },
    thursday: { type: daySchema },
    friday: { type: daySchema },
    saturday: { type: daySchema },
    sunday: { type: daySchema },
}, { _id: false });

const locationSchema = new mongoose.Schema({
    location: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    address: { type: String },
    country: { type: String },
    state: { type: String },
    city: { type: String },
    zip: { type: String },
    meevo_location_id: {
        type: String,
        default: null
    },
    meevo_tid: {
        type: Number,
        default: 200515
    },
    country_code: { type: Number },
    phone: { type: String },
    week_details: { type: weekDetailsSchema },
}, {
    timestamps: true
});

const Location = mongoose.model('Location', locationSchema);
module.exports = Location