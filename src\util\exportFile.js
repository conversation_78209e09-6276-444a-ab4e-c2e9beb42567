const { Parser } = require('json2csv');

const generateCSV = async (data, csvFields) => {
    try {
        const json2csvParser = csvFields
            ? new Parser({ fields: csvFields, encoding: 'utf-8', withBOM: true })
            : new Parser();

        const csvData = json2csvParser.parse(data);
        return Buffer.from(csvData).toString('base64');
    } catch (error) {
        throw new Error(`CSV generation failed: ${error.message}`);
        // or throw new AppError(error) if you're using a custom error handler
    }
};

module.exports = generateCSV;
