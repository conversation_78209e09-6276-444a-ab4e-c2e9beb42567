const { AdManagerService } = require("../service");


class AdManagerController {
    static async createAdManager(req, res, next) {
        try {
            const adManagerData = req.body;
            const id = req.id;
            const adManager = await AdManagerService.createAdManager(adManagerData, id);
            res.status(200).json(adManager);
        } catch (error) {
            next(error);
        }
    }

    static async updateAdManager(req, res, next) {
        try {
            const adManagerData = req.body;
            const adManager = await AdManagerService.updateAdManager(adManagerData);
            res.status(200).json(adManager);
        } catch (error) {
            next(error);
        }
    }

    static async getAdManagers(req, res, next) {
        try {
            const params = req.query;
            const locations = req.userLocations;
            const adManager = await AdManagerService.getAdManagers(params, locations);
            res.status(200).json(adManager);
        } catch (error) {
            next(error);
        }
    }

    static async getAdManagerById(req, res, next) {
        try {
            const managerId = req.params.id
            const adManager = await AdManagerService.getAdManagerById(managerId);
            res.status(200).send(adManager);
        } catch (error) {
            next(error);
        }
    }

    static async deleteAdManager(req, res, next) {
        try {
            const managerId = req.params.id
            const adManager = await AdManagerService.deleteAdManager(managerId);
            res.status(200).send(adManager);
        } catch (error) {
            next(error);
        }
    }
}

module.exports = AdManagerController