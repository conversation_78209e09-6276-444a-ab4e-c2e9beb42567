const express = require('express');
const { GhlPushController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { GHL } = require('../constant/enums');

class GhlPushRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(GHL), GhlPushController.getGHL);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = GhlPushRoute;
