const nodemailer = require('nodemailer');
const config = require('config');

const nodeMailerConfig = config.get('NODEMAILER');

const transporter = nodemailer.createTransport({
    host: nodeMailerConfig.HOST,
    port: nodeMailerConfig.PORT,
    auth: nodeMailerConfig.AUTH,
});

const emailSender = async (emailData) => {
    const contacts = {
        to: emailData.to,
        from: nodeMailerConfig.FROM_NAME,
        bcc: emailData.bcc,
    };

    const email = Object.assign(
        {},
        emailData.content,
        contacts,
        emailData.attachments ? { attachments: emailData.attachments } : {}
    );

    try {
        const message = await transporter.sendMail(email);
        console.info('Email sent:', message.response);
    } catch (err) {
        console.error('Error sending email:', err);
    }
};

transporter.verify((error) => {
    if (error) {
        console.error('SMTP server verification failed:', error.message);
    } else {
        console.info('SMTP server is verified and ready to send emails.');
    }
});

module.exports = {
    emailSender,
};
