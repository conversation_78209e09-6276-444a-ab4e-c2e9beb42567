const { LeadsService } = require('../service/index');

class LeadsController {
    static async getLeads(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const leads = await LeadsService.getLeads(queryParams, locations);
            res.status(200).json(leads);
        } catch (error) {
            next(error);
        }
    }

    static async exportLeadData(
        req, res, next
    ) {
        try {
            const locations = req.userLocations;
            const queryParams = req.query;
            const exportData = await LeadsService.exportLeadData(queryParams, locations);
            const dataBuffer = Buffer.from(exportData.data, 'base64');
            let fileName = `exported_Leads_${Date.now()}.csv`;
            let contentType = 'text/csv';

            res
                .setHeader('Content-Disposition', `attachment; filename=${fileName}`)
                .setHeader('Content-Type', contentType)
                .send(dataBuffer.toString('utf8'));
        } catch (error) {
            next(error);
        }
    }

    static async createLead(req, res, next) {
        try {
            const leadData = req.body;
            const lead = await LeadsService.createLead(leadData);
            res.status(200).send(lead);
        } catch (error) {
            next(error)
        }
    }

}

module.exports = LeadsController