const crypto = require('crypto');
const config = require('config');

const ENCRYPTION_KEY = config.get('ENCRYPTION.SECRET'); // Must be 32 bytes
const IV_LENGTH = config.get('ENCRYPTION.IV'); // Typically 16 bytes

const encryptEmail = (email) => {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(email);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return iv.toString('hex') + ':' + encrypted.toString('hex');
};

const decryptEmail = (text) => {
    const parts = text.split(':');
    if (parts.length !== 2) throw new Error('Invalid encrypted format');

    const iv = Buffer.from(parts[0], 'hex');
    const encryptedText = Buffer.from(parts[1], 'hex');

    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    const email = decrypted.toString()
    return email;
}

module.exports = {
    encryptEmail,
    decryptEmail
};
