const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const meevoProductSchema = new Schema({
    product_id: { type: String, required: true, unique: true }, // Ref to MeevoSale.cservice
    name: { type: String, required: true },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations', required: true }, // Ref to Location
}, {
    timestamps: true
});

const MeevoProduct = mongoose.model('meevoProduct', meevoProductSchema);

module.exports = MeevoProduct;