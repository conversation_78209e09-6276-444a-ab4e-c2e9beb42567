const { GhlPushService } = require('../service/index');

class GhlPushController {
    static async getGHL(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const ghldata = await GhlPushService.getGHLS(queryParams, locations);
            res.status(200).json(ghldata);
        } catch (error) {
            next(error);
        }
    }

}

module.exports = GhlPushController;
