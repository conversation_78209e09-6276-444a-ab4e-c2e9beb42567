const { baseListQuery } = require('../query/baseList.query');
const meevoAppointmentSchema = require('../model/meevoAppointments');
const { AppError, generateCSV } = require('../util/index');
const MeevoAppointment = require('../model/meevoAppointments');
const { getAppointmentsQuery, appointmentSearchFeilds } = require('../query/appointment.query');
const AppUser = require('../model/appUser');
const mongoose = require('mongoose');
const handleServiceError = require('../util/handleError');

class AppointmentService {
    static async getAppointments(params, locations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getAppointmentsQuery(params, locations),
                params,
                appointmentSearchFeilds,
                {},
            );
            const [appointments, count] = await Promise.all([
                MeevoAppointment.aggregate(query),
                MeevoAppointment.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: appointments,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async exportAppintents(
        queryParams,
        id
    ) {
        try {
            const { data } = await this.getAppointments(queryParams, id);
            const xlsxData = data.map((item) => ({
                Id: item._id || '',
                firstName: item.firstname || '',
                lastName: item.lastname || '',
                email: item.email || '',
                phone: item.phone || '',
                tagId: item.tag_id.join(',') || '',
                location: item.location.location || '',
                source: item.source || '',
                service_status: item.service_status || '',
                createdAt: item.createdAt || ''
            }));
            const xlsxFields = [
                'Id',
                'firstName',
                'lastName',
                'email',
                'phone',
                'tagId',
                'location',
                'source',
                'service_status',
                'createdAt',
            ];
            const excelFile = await generateCSV(xlsxData, xlsxFields);
            return {
                status: true,
                message: messageKey.exportSuccessfully,
                data: excelFile || [],
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

};

module.exports = AppointmentService