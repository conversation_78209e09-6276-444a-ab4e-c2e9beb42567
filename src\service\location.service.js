const { baseListQuery } = require('../query/baseList.query');
const { AppError } = require('../util/index');
const AppUser = require('../model/appUser');
const mongoose = require('mongoose');
const { locationSearchFields, getLocationQuery } = require('../query/location.query');
const Location = require('../model/location');
const { default: axios } = require('axios');
const handleServiceError = require('../util/handleError');

class LocationService {
    static async getLocations(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getLocationQuery(params, userLocations),
                params,
                locationSearchFields,
                {},
            );
            const [locations, count] = await Promise.all([
                Location.aggregate(query),
                Location.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: locations,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async createLocation(data) {
        try {
            const {
                location,
                meevo_location_id,
                isActive,
                address,
                country,
                state,
                city,
                zip
                //week_details,
                //phone,
                //country_code
            } = data;

            const existing = await Location.findOne({ meevo_location_id });
            if (existing) throw new AppError(409, 'record already exist')

            const newLocation = new Location({
                location,
                meevo_location_id,
                isActive,
                address,
                country,
                state,
                city,
                zip
                //week_details,
                //phone,
                //country_code
            });
            const savedLocation = await newLocation.save();

            return { message: 'Location created successfully', data: savedLocation, status: true };

        } catch (error) {
            handleServiceError(error)
        }
    }

    static async updateLocation(id, data) {
        try {
            const {
                location,
                meevo_location_id,
               // isActive,
                address,
                country,
                state,
                city,
                zip,
                //week_details,
                //phone,
                //country_code
            } = data;
            const existing = await Location.findOne({ _id: new mongoose.Types.ObjectId(id) });
            if (!existing) throw new AppError(404, 'record does not exist')

            const updatedLocation = await Location.findOneAndUpdate(
                { _id: new mongoose.Types.ObjectId(id) },
                {
                    location,
                    meevo_location_id,
                   // isActive,
                    address,
                    country,
                    state,
                    city,
                    zip,
                   // week_details,
                    //phone,
                    //country_code
                }, { new: true }
            );
            return { message: 'Location updated successfully', data: updatedLocation, status: true };

        } catch (error) {
            handleServiceError(error)
        }
    }

    static async getLocationById(id) {
        try {
            const existing = await Location.findOne({ _id: new mongoose.Types.ObjectId(id) });
            if (!existing) throw new AppError(404, 'Location does not exist')

            return { message: 'Request completed successfully', data: existing, status: true };

        } catch (error) {
            handleServiceError(error)
        }
    }

    static async getLocationByMeevoLoctionId(locationId) {
        try {
            const response = await axios.post(
                'https://marketplace.meevo.com/oauth2/token',
                null,
                {
                    params: {
                        client_id: 'd07624a8-b954-4fce-88e3-04792757d1b5',
                        client_secret: '42023db5-6726-4359-9d74-80044c35ca1b',
                        format: 'json'
                    }
                }
            );

            const token = response.data?.access_token;
            if (!token) throw new AppError(401, 'UnAuthorized')
            const { data } = await axios.get(`https://na1pub.meevo.com/publicapi/v1/businessInformation?TenantId=200515&LocationId=${locationId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                }
            )
            if (!data) throw new AppError(403, 'Bad Request')
            const addressObj = data.data.address;
            const weekdaysMap = {
                Monday: 'monday',
                Tuesday: 'tuesday',
                Wednesday: 'wednesday',
                Thursday: 'thursday',
                Friday: 'friday',
                Saturday: 'saturday',
                Sunday: 'sunday',
            };

            const weekDetails = {};

            data.data.businessHours.forEach(day => {
                const key = weekdaysMap[day.weekdayNameDisplay];
                if (key) {
                    weekDetails[key] = {
                        openTime: day.startTime.split('T')[1].slice(0, 5),
                        closeTime: day.endTime.split('T')[1].slice(0, 5),
                    };
                }
            });
            const transformedData = {
                location: data.data.storeName,
                isActive: true,
                address: [addressObj.address1, addressObj.address2].filter(Boolean).join(', '),
                country: addressObj.country,
                state: addressObj.state,
                city: addressObj.city,
                zip: addressObj.zipCode,
                meevo_location_id: data.data.locationId,
                meevo_tid: data.data.tenantId,
                country_code: parseInt(data.data.countryCode),
                phone: data.data.phoneNumber,
                week_details: weekDetails,
            };
            return { status: true, data: transformedData, message: 'Request Completed Successfully' }
        } catch (error) {
            if (error.response?.status === 401 || error.response?.status === 403) {
                error = new AppError(404, 'Location is not valid. No location associated with this ID');
            }
            handleServiceError(error)
        }
    }
};

module.exports = LocationService