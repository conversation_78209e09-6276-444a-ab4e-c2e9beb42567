const { AppointmentService } = require('../service/index');

class AppointmentController {
    static async getAppointments(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const appontments = await AppointmentService.getAppointments(queryParams, locations);
            res.status(200).json(appontments);
        } catch (error) {
            next(error);
        }
    }

    static async exportAppintents(
        req, res, next
    ) {
        try {
            const locations = req.userLocations;
            const queryParams = req.params;
            const exportData = await AppointmentService.exportAppintents(queryParams, locations);
            const dataBuffer = Buffer.from(exportData.data, 'base64');
            let fileName = `exported_Appointments_${Date.now()}.csv`;
            let contentType = 'text/csv';

            res
                .setHeader('Content-Disposition', `attachment; filename=${fileName}`)
                .setHeader('Content-Type', contentType)
                .send(dataBuffer.toString('utf8'));
        } catch (error) {
            next(error);
        }
    }
}

module.exports = AppointmentController