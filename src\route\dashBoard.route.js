const express = require('express');
const { Authentication } = require('../Middleware/index');
const DashBoardController = require('../controller/dashBoard.controller');
const { NOPERMISSION } = require('../constant/enums');
const PermissionMiddleware = require('../Middleware/permission.middleware');
class DashBoardRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/graph-details', Authentication.authUser, PermissionMiddleware.checkPermission(NOPERMISSION), DashBoardController.getDashBoardDetails);
        this.router.get('/leader-board', Authentication.authUser, PermissionMiddleware.checkPermission(NOPERMISSION), DashBoardController.leaderBoard);
        this.router.get('/export-leader-board', Authentication.authUser, PermissionMiddleware.checkPermission(NOPERMISSION), DashBoardController.exportLeaderBoard);
        this.router.get('/lead-booking-caller', Authentication.authUser, PermissionMiddleware.checkPermission(NOPERMISSION), DashBoardController.leadBooking);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = DashBoardRoute;
