const mongoose = require("mongoose");

const getAdManagers = (params, userLocations) => [
  {
    $match: {
      isDeleted: false,
      ...(params.source_Id ? { sourceId: new mongoose.Types.ObjectId(params.source_Id) } : {}),
      ...(params.location_Id
        ? { locationId: new mongoose.Types.ObjectId(params.location_Id) }
        : userLocations?.length
          ? { locationId: { $in: userLocations } }
          : {}),
      ...(params.dateFrom && params.dateEnd
        ? {
          createdAt: {
            $gte: new Date(params.dateFrom),
            $lte: new Date(params.dateEnd),
          },
        }
        : params.dateFrom
          ? {
            createdAt: {
              $gte: new Date(params.dateFrom),
            },
          }
          : {}),
    },
  },
  {
    $lookup: {
      from: 'locations',
      localField: 'locationId',
      foreignField: '_id',
      as: 'location',
    },
  },
  { $unwind: '$location' },
  {
    $lookup: {
      from: 'sources',
      localField: 'sourceId',
      foreignField: '_id',
      as: 'source',
    },
  },
  { $unwind: '$source' },

  { $unwind: '$ads' },
  {
    $lookup: {
      from: 'leads',
      let: { currentAdId: '$ads.id' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$adId', '$$currentAdId'] }
          }
        },
        {
          $group: {
            _id: null,
            totalLeads: { $sum: 1 },
            existingLeads: {
              $sum: {
                $cond: [{ $ne: ['$lastVisitDate', null] }, 1, 0]
              }
            },
            newLeads: {
              $sum: {
                $cond: [{ $eq: ['$lastVisitDate', null] }, 1, 0]
              }
            },
            contactedLeads: {
              $sum: {
                $cond: [
                  { $ne: ['$service_status', 'NEEDS_ACTION'] },
                  1,
                  0
                ]
              }
            },
            spam: {
              $sum: {
                $cond: [{ $eq: ['$service_status', 'SPAM'] }, 1, 0]
              }
            },
            // ✅ conversion = leads with lastVisitDate != null
            conversion: {
              $sum: {
                $cond: [{ $ne: ['$lastVisitDate', null] }, 1, 0]
              }
            }
          }
        }
      ],
      as: 'leadStats'
    }
  },
  {
    $addFields: {
      existingLeads: { $ifNull: [{ $arrayElemAt: ['$leadStats.existingLeads', 0] }, 0] },
      newLeads: { $ifNull: [{ $arrayElemAt: ['$leadStats.newLeads', 0] }, 0] },
      contactedLeads: { $ifNull: [{ $arrayElemAt: ['$leadStats.contactedLeads', 0] }, 0] },
      spam: { $ifNull: [{ $arrayElemAt: ['$leadStats.spam', 0] }, 0] },
      conversion: { $ifNull: [{ $arrayElemAt: ['$leadStats.conversion', 0] }, 0] } // ✅ new conversion field
    }
  },

  {
    $addFields: {
      locationName: '$location.location',
      sourceName: '$source.sourceName',
      leads: { $ifNull: ['$leads', 0] },
      views: { $ifNull: ['$views', 0] },
    },
  },

  // ✅ ROI calculation
  {
    $addFields: {
      finalROI: {
        $cond: [
          { $eq: ['$ads.ROI', 0] },
          0,
          {
            $concat: [
              {
                $toString: {
                  $round: [
                    { $multiply: [{ $divide: ['$ads.spend', '$ads.ROI'] }, 100] },
                    0
                  ]
                }
              },
              '%'
            ]
          }
        ]
      }
    }
  },

  {
    $project: {
      _id: 1,
      sourceId: 1,
      locationId: 1,
      email: 1,
      notificationEmail: 1,
      formId: 1,
      campaignId: '$ads.campaign_id',
      campaignGroupId: 1,
      createdAt: 1,
      locationName: 1,
      sourceName: 1,
      leads: '$ads.leads',
      views: '$ads.impressions',
      conversion: 1, // ✅ include conversion in response
      isActive: '$ads.status',
      adSetId: '$adId',
      adId: '$ads.id',
      name: '$ads.name',
      spam: 1,
      image_url: '$ads.image_url',
      existingLeads: 1,
      newLeads: 1,
      contactedLeads: 1,
      spend: '$ads.spend',
      ROI: '$ads.ROI',
      finalROI: 1
    },
  },
];


const getAdManagerByIdQuey = (id) => [
  {
    $match: {
      _id: new mongoose.Types.ObjectId(id),
      isDeleted: false
    }
  },
  {
    $lookup: {
      from: 'locations',
      localField: 'locationId',
      foreignField: '_id',
      as: 'location'
    }
  },
  {
    $unwind: {
      path: '$location',
    }
  },
  {
    $lookup: {
      from: 'sources',
      localField: 'sourceId',
      foreignField: '_id',
      as: 'source'
    }
  },
  {
    $unwind: {
      path: '$source',
    }
  },
  {
    $addFields: {
      isActiveOrder: {
        $cond: [
          { $eq: ['$isActive', 'ACTIVE'] },
          0,
          { $cond: [{ $eq: ['$isActive', 'PAUSED'] }, 1, 2] }
        ]
      },
      locationName: {
        $cond: [
          { $ifNull: ['$location.location', false] },
          '$location.location',
          null
        ]
      },
      meevo_Tenant_Id: {
        $cond: [
          { $ifNull: ['$location.meevo_tid', false] },
          '$location.meevo_tid',
          null
        ]
      },
      meevo_location: {
        $cond: [
          { $ifNull: ['$location.meevo_location_id', false] },
          '$location.meevo_location_id',
          null
        ]
      },
      sourceName: {
        $cond: [
          { $ifNull: ['$source.sourceName', false] },
          '$source.sourceName',
          null
        ]
      },
      leads: { $ifNull: ['$leads', 0] },
      views: { $ifNull: ['$views', 0] },
      conversion: { $ifNull: ['$conversion', 0] } // ✅ ensure conversion present in single ad fetch too
    }
  },
  {
    $sort: { isActiveOrder: 1 }
  },
  {
    $project: {
      _id: 1,
      sourceId: 1,
      locationId: 1,
      meevo_location: 1,
      meevo_Tenant_Id: 1,
      email: 1,
      notificationEmail: 1,
      isActive: 1,
      adId: 1,
      formId: 1,
      campaignId: 1,
      campaignGroupId: 1,
      createdAt: 1,
      locationName: 1,
      sourceName: 1,
      leads: 1,
      views: 1,
      conversion: 1
    }
  }
];

const adManagerSearchFeilds = [
  "_id",
  "sourceId",
  "locationId",
  "email",
  "notificationEmail",
  "isActive",
  "adSetId",
  "formId",
  "campaignId",
  "campaignGroupId",
  "createdAt",
  "locationName",
  "sourceName",
  "leads",
  "views",
  "conversion"
]

module.exports = {
  getAdManagers,
  adManagerSearchFeilds,
  getAdManagerByIdQuey
}
