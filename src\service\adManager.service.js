const { AppError } = require('../util/index');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const AdManager = require('../model/adManager');
const AppUser = require('../model/appUser');
const { getAdManagers, adManagerSearchFeilds, getAdManagerByIdQuey } = require('../query/adManager.query');
const { baseListQuery } = require('../query/baseList.query');
const handleServiceError = require('../util/handleError');

class AdManagerService {
    static async getAdManagers(params, locations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getAdManagers(params, locations),
                params,
                adManagerSearchFeilds,
                {},
            );
            const [adManagers, count] = await Promise.all([
                AdManager.aggregate(query),
                AdManager.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: adManagers,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

      static async createAdManager(adManagerData, superAdminId) {
        try {
          const {
            sourceId,
            locationId,
            email,
            notificationEmail,
            status,
            adsAdId,
            adId,
            formId,
            campaignGroupId,
            campaignId
          } = adManagerData;
      
          // Step 1: Check if AdManager with same adSetId exists
          const existing = await AdManager.findOne({ adId, isDeleted: false });
      
          // Step 2: If exists, check if the adId is already present in the ads array
          if (existing) {
            const isAlreadyPresent = existing.ads?.some(ad => ad.id === adsAdId);
            if (isAlreadyPresent) {
              // ❌ adSetId + adId combo already exists
              throw new AppError(400, `AdManager with adSetId  and adId "${adId}" already exists.`);
            }
            // ✅ Update existing with new ad
            const newAd = { id: adsAdId, status };
            existing.ads.push(newAd);
            existing.updatedBy = new mongoose.Types.ObjectId(superAdminId);
            await existing.save();
      
            return {
              message: 'AdManager updated with new adId successfully',
              user: existing,
              status: true
            };
          }
      
          // Step 3: Create new AdManager if adSetId not found
          const newAd = { id: adId, status };
          const newAdManager = await AdManager.create({
            sourceId,
            locationId,
            email,
            notificationEmail,
            adId,
            formId,
            campaignGroupId,
            campaignId,
            createdBy: new mongoose.Types.ObjectId(superAdminId),
            updatedBy: new mongoose.Types.ObjectId(superAdminId),
            ads: [newAd]
          });
      
          return {
            message: 'AdManager created successfully with new adSetId',
            user: newAdManager,
            status: true
          };
      
        } catch (err) {
          handleServiceError(err);
        }
      }
      
    static async updateAdManager(admanager) {
        try {
          const result = await AdManager.updateOne(
            {
              adId: admanager.adSetId,        // Match top-level AdManager's adId (i.e., adSetId)
              'ads.id': admanager.adId        // Match nested ad inside ads array
            },
            {
              $set: {
                'ads.$.status': admanager.status   // Only update status field
              }
            }
          );
      
          if (result.modifiedCount === 0) {
            throw new AppError(404, 'Ad Manager or Ad not found');
          }
      
          return {
            message: 'Ad status updated successfully',
            status: true
          };
      
        } catch (error) {
          handleServiceError(error);
        }
      }
      

    static async getAdManagerById(id) {
        try {
            const [admanager] = await AdManager.aggregate(getAdManagerByIdQuey(id))
            if (!admanager)
                throw new AppError(404, 'Ad Manager not found');
            return { status: true, message: 'Request Completed successfully', data: admanager };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async deleteAdManager(id) {
        try {
            const existing = await AdManager.findOne({ _id: new mongoose.Types.ObjectId(id), isDeleted: false });
            if (!existing) {
                throw new AppError(404, 'Ad Manager does not exist');
            }
            existing.isDeleted = true;
            existing.save({});
            return { status: true, message: 'Ad Manager deleted successfully' };
        } catch (error) {
            handleServiceError(error)
        }
    }
};

module.exports = AdManagerService