const mongoose = require('mongoose');
const config = require('config');
const Customer = require('../src/model/customer');
const { 
    getGHLDataQuery, 
    getGHLDataQueryOptimized, 
    getGHLDataQueryLightweight, 
    getGHLDataCountQuery 
} = require('../src/query/ghlPush.query');

async function testGHLPerformance() {
    try {
        // Connect to MongoDB
        const MONGODB_URI = config.get('DB_URL');
        await mongoose.connect(MONGODB_URI);
        console.log('Connected to MongoDB');

        // Test parameters
        const testParams = {
            pageNum: 1,
            pageLimit: 10,
            sortField: 'createdAt',
            sortOrder: 'desc'
        };
        const userLocations = []; // Empty for testing all locations
        const skip = 0;
        const limit = 10;

        console.log('\n🧪 Testing GHL Query Performance...\n');

        // Test 1: Original Query
        console.log('1️⃣  Testing Original GHL Query...');
        const start1 = Date.now();
        const originalQuery = getGHLDataQuery(testParams, userLocations, skip, limit);
        const originalResults = await Customer.aggregate(originalQuery);
        const time1 = Date.now() - start1;
        console.log(`   ✓ Original Query: ${time1}ms (${originalResults.length} records)`);

        // Test 2: Optimized Query
        console.log('2️⃣  Testing Optimized GHL Query...');
        const start2 = Date.now();
        const optimizedQuery = getGHLDataQueryOptimized(testParams, userLocations, skip, limit);
        const optimizedResults = await Customer.aggregate(optimizedQuery);
        const time2 = Date.now() - start2;
        console.log(`   ✓ Optimized Query: ${time2}ms (${optimizedResults.length} records)`);

        // Test 3: Lightweight Query
        console.log('3️⃣  Testing Lightweight GHL Query...');
        const start3 = Date.now();
        const lightweightQuery = getGHLDataQueryLightweight(testParams, userLocations, skip, limit);
        const lightweightResults = await Customer.aggregate(lightweightQuery);
        const time3 = Date.now() - start3;
        console.log(`   ✓ Lightweight Query: ${time3}ms (${lightweightResults.length} records)`);

        // Test 4: Count Query
        console.log('4️⃣  Testing GHL Count Query...');
        const start4 = Date.now();
        const countQuery = getGHLDataCountQuery(testParams, userLocations);
        const countResults = await Customer.aggregate(countQuery);
        const time4 = Date.now() - start4;
        const totalCount = countResults[0]?.totalCount || 0;
        console.log(`   ✓ Count Query: ${time4}ms (${totalCount} total records)`);

        // Performance Summary
        console.log('\n📊 GHL Performance Summary:');
        console.log('─'.repeat(50));
        console.log(`Original Query:    ${time1}ms (baseline)`);
        console.log(`Optimized Query:   ${time2}ms (${((time1 - time2) / time1 * 100).toFixed(1)}% faster)`);
        console.log(`Lightweight Query: ${time3}ms (${((time1 - time3) / time1 * 100).toFixed(1)}% faster)`);
        console.log(`Count Query:       ${time4}ms (${((time1 - time4) / time1 * 100).toFixed(1)}% faster)`);

        // Test with filters
        console.log('\n🔍 Testing GHL with Filters...');
        const filteredParams = {
            ...testParams,
            gPlusBadge: 'true',
            YTDmin: 100
        };

        const start5 = Date.now();
        const filteredQuery = getGHLDataQueryOptimized(filteredParams, userLocations, skip, limit);
        const filteredResults = await Customer.aggregate(filteredQuery);
        const time5 = Date.now() - start5;
        console.log(`   ✓ Filtered Query: ${time5}ms (${filteredResults.length} records)`);

        // Test different page sizes
        console.log('\n📄 Testing Different Page Sizes...');
        const pageSizes = [5, 10, 25, 50];
        
        for (const pageSize of pageSizes) {
            const start = Date.now();
            const query = getGHLDataQueryOptimized(testParams, userLocations, 0, pageSize);
            const results = await Customer.aggregate(query);
            const time = Date.now() - start;
            console.log(`   ✓ Page size ${pageSize}: ${time}ms (${results.length} records)`);
        }

        // Test search functionality
        console.log('\n🔎 Testing Search Performance...');
        const searchParams = {
            ...testParams,
            email: 'test' // Search term
        };

        const start6 = Date.now();
        const searchQuery = getGHLDataQueryOptimized(searchParams, userLocations, skip, limit);
        const searchResults = await Customer.aggregate(searchQuery);
        const time6 = Date.now() - start6;
        console.log(`   ✓ Search Query: ${time6}ms (${searchResults.length} records)`);

        // Index usage check
        console.log('\n🗂️  Checking Index Usage...');
        const explainResult = await Customer.collection.aggregate([
            { $match: { ghl_push: true } },
            { $sort: { createdAt: -1 } },
            { $limit: 10 }
        ]).explain('executionStats');

        const executionStats = explainResult.stages[0].$cursor.executionStats;
        console.log(`   ✓ Total docs examined: ${executionStats.totalDocsExamined}`);
        console.log(`   ✓ Total docs returned: ${executionStats.totalDocsReturned}`);
        console.log(`   ✓ Execution time: ${executionStats.executionTimeMillis}ms`);
        console.log(`   ✓ Index used: ${executionStats.executionStages.indexName || 'No index'}`);

        // Memory usage test
        console.log('\n💾 Memory Usage:');
        const memUsage = process.memoryUsage();
        console.log(`   RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
        console.log(`   Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
        console.log(`   Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);

        console.log('\n✅ GHL Performance testing completed!');

    } catch (error) {
        console.error('❌ Error during GHL performance testing:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

// Run the test
if (require.main === module) {
    testGHLPerformance();
}

module.exports = testGHLPerformance;
