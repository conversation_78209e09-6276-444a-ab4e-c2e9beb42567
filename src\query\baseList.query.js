const mongoose = require('mongoose');
const { Types } = mongoose;

// async function baseListQuery(baseQuery, queryParams, searchFields, filterFields, options = {}) {
//     const defaultOpt = {
//         ignorePagination: false,
//         ignoreSearch: false,
//         ignoreFilter: false,
//         ...options,
//     };

//     const query = [
//         ...baseQuery,
//     ];

//     let filters = {};
//     if (Object.keys(filterFields && queryParams).length && !defaultOpt.ignoreFilter) {
//         filters = await getFilterQuery(filterFields, queryParams);
//     }

//     if (searchFields.length && queryParams.search && !defaultOpt.ignoreSearch) {
//         const searchQuery = await getSearchQuery(searchFields, queryParams.search);
//         if (searchQuery?.length) filters.$or = searchQuery;
//     }

//     if (Object.keys(filters).length) {
//         const match = { $match: filters };
//         const length = query.length - 1 > -1 ? query.length - 1 : 0;
//         query.splice(length, 0, match);
//     }

//     let countQuery = [...query, {
//         $group: {
//             _id: null,
//             totalCount: { $sum: 1 },
//         },
//     }];

//     const pageNum = queryParams.pageNum ? parseInt(queryParams.pageNum) : 1;
//     const limit = queryParams.pageLimit ? parseInt(queryParams.pageLimit) : 100;

//     const sortField = queryParams?.sortField || '_id';
//     const sortOrder = queryParams.sortOrder === 'asc' ? 1 : -1;
//     const sort = {
//         [sortField]: sortOrder,
//         ...(sortField !== 'name' && { name: 1 }),
//     };

//     // Add sort stage
//     query.push({ $sort: sort });

//     if (!defaultOpt.ignorePagination && queryParams?.pageLimit !== -1) {
//         const skip = limit * (pageNum - 1);
//         // Add pagination stages after sort
//         query.push({ $skip: skip }, { $limit: limit });
//     }

//     return { query, pageNum, limit, countQuery };
// }

async function baseListQuery(baseQuery, queryParams, searchFields, filterFields, options = {}) {
    const defaultOpt = {
        ignorePagination: false,
        ignoreSearch: false,
        ignoreFilter: false,
        ...options,
    };

    const pageNum = queryParams.pageNum ? parseInt(queryParams.pageNum) : 1;
    const limit = queryParams.pageLimit ? parseInt(queryParams.pageLimit) : 100;
    const skip = limit * (pageNum - 1);

    // ✅ If baseQuery is a function (like getGHLDataQuery), pass skip & limit
    const query = typeof baseQuery === 'function'
        ? baseQuery(queryParams, options.userLocations, skip, limit)   // <-- pass skip & limit
        : [...baseQuery]; // else treat it as a static array

    // 🔹 Dynamic Filters
    let filters = {};
    if (Object.keys(filterFields && queryParams).length && !defaultOpt.ignoreFilter) {
        filters = await getFilterQuery(filterFields, queryParams);
    }
    if (searchFields.length && queryParams.search && !defaultOpt.ignoreSearch) {
        const searchQuery = await getSearchQuery(searchFields, queryParams.search);
        if (searchQuery?.length) filters.$or = searchQuery;
    }
    if (Object.keys(filters).length) {
        // Insert filters early, ideally after first $match
        const firstMatchIndex = query.findIndex(stage => stage.$match);
        const insertIndex = firstMatchIndex !== -1 ? firstMatchIndex + 1 : 0;
        query.splice(insertIndex, 0, { $match: filters });
    }

    // ✅ Count Query (lightweight: only count)
    // const countQuery = [
    //     { $match: { ghl_push: true, ...(queryParams.email ? { email: queryParams.email } : {}) } },
    //     { $count: "totalCount" }
    // ];
    const countQuery = query.filter(stage =>
        !stage.$skip && !stage.$limit && !stage.$sort
    );
    countQuery.push({ $count: "totalCount" });

    return { query, pageNum, limit, countQuery };
}


async function getFilterQuery(filterFields, queryParams) {
    if (typeof filterFields !== 'object') return {};
    const filters = { $and: [] };
    const filterKeys = Object.keys(filterFields);

    for (const field of filterKeys) {
        const trimmedField = field.trim();
        const value = queryParams[trimmedField]?.trim();
        if (!value) continue;

        const type = filterFields[trimmedField].type;
        if (type === 'string') {
            const values = value.split(',').map(v => v.trim());
            const condition =
                values.length > 1
                    ? {
                        $or: values.map(val => ({
                            [trimmedField]: { $regex: val, $options: 'i' },
                        })),
                    }
                    : { [trimmedField]: { $regex: values[0], $options: 'i' } };

            filters.$and.push(condition);
        } else if (type === 'objectId') {
            const ids = value.split(',').map(v => Types.ObjectId.createFromHexString(v.trim()));
            filters.$and.push({ [trimmedField]: { $in: ids } });
        } else if (type === 'date') {
            try {
                const DATE = new Date(value);
                const date = DATE.getDate();
                const month = DATE.getMonth();
                const year = DATE.getFullYear();

                const startDate = new Date(Date.UTC(year, month, date, 0, 0, 0, 0));
                const endDate = new Date(Date.UTC(year, month, date, 23, 59, 59, 999));

                const condition = {
                    [field]: { $gte: startDate, $lte: endDate },
                };

                filters.$and.push(condition);
            } catch (err) {
                continue;
            }
        }
    }

    return filters.$and.length ? filters : {};
}

async function getSearchQuery(searchFields, searchString) {
    const searchObjects = [];
    for (const field of searchFields) {
        const escaped = searchString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        searchObjects.push({
            [field.trim()]: { $regex: escaped, $options: 'i' },
        });
    }
    return searchObjects;
}

module.exports = {
    baseListQuery,
};
