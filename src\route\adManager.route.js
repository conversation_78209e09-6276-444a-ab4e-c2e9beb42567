const express = require('express');
const { AdManagerController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { AD_MANAGER } = require('../constant/enums');

class AdManagerRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(AD_MANAGER), AdManagerController.getAdManagers);
        this.router.get('/:id', Authentication.authUser, PermissionMiddleware.checkPermission(AD_MANAGER), AdManagerController.getAdManagerById);
        this.router.delete('/:id', Authentication.authUser, PermissionMiddleware.checkPermission(AD_MANAGER), AdManagerController.deleteAdManager);
        this.router.post('/', Authentication.authUser, PermissionMiddleware.checkPermission(AD_MANAGER), AdManagerController.createAdManager);
        this.router.put('/:adManagerId', Authentication.authUser, PermissionMiddleware.checkPermission(AD_MANAGER), AdManagerController.updateAdManager)
    }

    getRoute() {
        return this.router;
    }
}

module.exports = AdManagerRoute;
