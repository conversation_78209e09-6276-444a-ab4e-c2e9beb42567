const { baseListQuery } = require('../query/baseList.query');
const { AppError, generateCSV } = require('../util/index');
const Location = require('../model/location')
const Lead = require('../model/lead');
const { default: axios } = require('axios');
const { getLeadDashboardData, getLeaderBoardQuery, leaderBoardSearchFeilds, getLeadBookingQuery } = require('../query/dashBoard.query');
const handleServiceError = require('../util/handleError');
const AppUser = require('../model/appUser');

class DashBoardService {
    static async getLeadDashboardData(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const leads = await Lead.aggregate(getLeadDashboardData(params, userLocations))
            const data = leads[0].data;
            const totalLeads = leads[0].total[0]?.count || 0;
            return {
                status,
                data: {
                    totalLeads,
                    data: data,
                    dayPassSubmission: 123,
                    lead50offvet: 123
                },
                message,
            }
        } catch (error) {
            throw new AppError(error)
        }
    }

    static async getLeaderBoard(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getLeaderBoardQuery(params, userLocations),
                params,
                leaderBoardSearchFeilds,
                {},
            );
            const [leads, count] = await Promise.all([
                Location.aggregate(query),
                Location.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: leads,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }
    static async getleadbooking(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            
            // Use Lead collection + query
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getLeadBookingQuery(params, userLocations),
                params,
                leaderBoardSearchFeilds,
                {},
            );

            const [leads, count] = await Promise.all([
                AppUser.aggregate(query),       // <-- FIXED to use Lead
                AppUser.aggregate(countQuery),  // <-- FIXED to use Lead
            ]);

            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            
            return {
                status,
                data: leads,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async exportLeaderBoardData(
        queryParams,
        loc
    ) {
        try {
            const { data } = await this.getLeaderBoard(queryParams, loc);
            const xlsxData = data.map(item => ({
                Id: item._id || '',
                Location: item.location || '',
                BookedLeads: item.bookedLeads ?? 0,
                MTDLeads: item.mtdLeads ?? 0,
                PrevMonthBookedLeads: item.prevMonthBookedLeads ?? 0,
                LeadsCalledCount: item.leadsCalledCount ?? 0,
                ConversionRateLast7Days: item.conversionRateLast7Days?.toFixed(2) || '0.00',
                ConversionRatePrevMonth: item.conversionRatePrevMonth?.toFixed(2) || '0.00',
            }));

            const xlsxFields = [
                'Id',
                'Location',
                'BookedLeads',
                'MTDLeads',
                'PrevMonthBookedLeads',
                'LeadsCalledCount',
                'ConversionRateLast7Days',
                'ConversionRatePrevMonth',
            ];

            const excelFile = await generateCSV(xlsxData, xlsxFields);

            return {
                status: true,
                message: 'request completed successfully',
                data: excelFile || [],
            }
        } catch (error) {
            handleServiceError(error)
        }
    }
};

module.exports = DashBoardService