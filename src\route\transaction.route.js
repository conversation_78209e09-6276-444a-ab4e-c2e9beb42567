const express = require('express');
const { Authentication } = require('../Middleware/index');
const TransactionController = require('../controller/transaction.controller');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { TRANSACTION } = require('../constant/enums');

class TransactionRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(TRANSACTION), TransactionController.getTransactionDetails);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = TransactionRoute;
