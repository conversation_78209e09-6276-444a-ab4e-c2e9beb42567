const TransactionService = require('../service/transaction.service');

class TransactionController {
    static async getTransactionDetails(req, res, next) {
        try {
            const locations = req.userLocations;
            const location = await TransactionService.getTransaction(locations);
            res.status(200).json(location);
        } catch (error) {
            next(error);
        }
    }
}

module.exports = TransactionController