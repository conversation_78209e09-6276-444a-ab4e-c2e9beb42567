const { UserService } = require('../service/index');

class UserController {
    static async signUp(req, res, next) {
        try {
            const userData = req.body;
            const newUser = await UserService.signUp(userData);
            res.status(200).send(newUser);
        } catch (error) {
            next(error);
        }
    }

    static async login(req, res, next) {
        try {
            const loginData = req.body;
            const token = await UserService.login(loginData);

            res.status(200).json(token);
        } catch (error) {
            next(error);
        }
    }

    static async updateUser(req, res, next) {
        try {
            const id = req.id;
            const userData = req.body;
            const updatedUser = await UserService.updateUser(id, userData);
            res.status(200).send(updatedUser);
        } catch (error) {
            next(error);
        }
    }

    static async createUserSuperAdmin(req, res, next) {
        try {
            const id = req.id;
            const userData = req.body;
            const user = await UserService.creatUserBySuperAdmin(userData, id);
            res.status(200).send(user);
        } catch (error) {
            next(error);
        }
    }

    static async updateUserByAdmin(req, res, next) {
        try {
            const id = req.id;
            const userData = req.body;
            const userId = req.params.userId
            const user = await UserService.updateUserBySuperAdmin(userId, userData, id);
            res.status(200).send(user);
        } catch (error) {
            next(error);
        }
    }

    static async getAllAppUsers(req, res, next) {
        try {
            const locations = req.userLocations;
            const params = req.query
            const user = await UserService.getAllAppUsers(params, locations);
            res.status(200).send(user);
        } catch (error) {
            next(error);
        }
    }

    static async getAppUserById(req, res, next) {
        try {
            const userId = req.params.id
            const user = await UserService.getAppUserById(userId);
            res.status(200).send(user);
        } catch (error) {
            next(error);
        }
    }

    static async getDropdowns(req, res, next) {
        try {
            const userId = req.id
            const dropdown = await UserService.getDropdown(userId);
            res.status(200).send(dropdown);
        } catch (error) {
            next(error);
        }
    }

    static async requestResetPassword(req, res, next) {
        try {
            const data = req.body;
            const password = await UserService.requestResetPassword(data);
            res.status(200).send(password);
        } catch (error) {
            next(error);
        }
    }

    static async resetPassword(req, res, next) {
        try {
            const data = req.body;
            const password = await UserService.resetPassword(data);
            res.status(200).send(password);
        } catch (error) {
            next(error);
        }
    }

}

module.exports = UserController;
