const mongoose = require('mongoose');
const config = require('config');
const Lead = require('../src/model/lead');
const { 
    getLeadsQuery, 
    getLeadsQueryOptimized, 
    getLeadsQueryLightweight, 
    getLeadsCountQuery 
} = require('../src/query/leads.query');

async function testLeadPerformance() {
    try {
        // Connect to MongoDB
        const MONGODB_URI = config.get('DB_URL');
        await mongoose.connect(MONGODB_URI);
        console.log('Connected to MongoDB');

        // Test parameters
        const testParams = {
            pageNum: 1,
            pageLimit: 10,
            sortField: 'createdAt',
            sortOrder: 'desc'
        };
        const userLocations = []; // Empty for testing all locations
        const skip = 0;
        const limit = 10;

        console.log('\n🧪 Testing Lead Query Performance...\n');

        // Test 1: Original Query
        console.log('1️⃣  Testing Original Query...');
        const start1 = Date.now();
        const originalQuery = getLeadsQuery(testParams, userLocations, skip, limit);
        const originalResults = await Lead.aggregate(originalQuery);
        const time1 = Date.now() - start1;
        console.log(`   ✓ Original Query: ${time1}ms (${originalResults.length} records)`);

        // Test 2: Optimized Query
        console.log('2️⃣  Testing Optimized Query...');
        const start2 = Date.now();
        const optimizedQuery = getLeadsQueryOptimized(testParams, userLocations, skip, limit);
        const optimizedResults = await Lead.aggregate(optimizedQuery);
        const time2 = Date.now() - start2;
        console.log(`   ✓ Optimized Query: ${time2}ms (${optimizedResults.length} records)`);

        // Test 3: Lightweight Query
        console.log('3️⃣  Testing Lightweight Query...');
        const start3 = Date.now();
        const lightweightQuery = getLeadsQueryLightweight(testParams, userLocations, skip, limit);
        const lightweightResults = await Lead.aggregate(lightweightQuery);
        const time3 = Date.now() - start3;
        console.log(`   ✓ Lightweight Query: ${time3}ms (${lightweightResults.length} records)`);

        // Test 4: Count Query
        console.log('4️⃣  Testing Count Query...');
        const start4 = Date.now();
        const countQuery = getLeadsCountQuery(testParams, userLocations);
        const countResults = await Lead.aggregate(countQuery);
        const time4 = Date.now() - start4;
        const totalCount = countResults[0]?.totalCount || 0;
        console.log(`   ✓ Count Query: ${time4}ms (${totalCount} total records)`);

        // Performance Summary
        console.log('\n📊 Performance Summary:');
        console.log('─'.repeat(50));
        console.log(`Original Query:    ${time1}ms (baseline)`);
        console.log(`Optimized Query:   ${time2}ms (${((time1 - time2) / time1 * 100).toFixed(1)}% faster)`);
        console.log(`Lightweight Query: ${time3}ms (${((time1 - time3) / time1 * 100).toFixed(1)}% faster)`);
        console.log(`Count Query:       ${time4}ms (${((time1 - time4) / time1 * 100).toFixed(1)}% faster)`);

        // Test with filters
        console.log('\n🔍 Testing with Filters...');
        const filteredParams = {
            ...testParams,
            service_status: 'NEEDS_ACTION'
        };

        const start5 = Date.now();
        const filteredQuery = getLeadsQueryOptimized(filteredParams, userLocations, skip, limit);
        const filteredResults = await Lead.aggregate(filteredQuery);
        const time5 = Date.now() - start5;
        console.log(`   ✓ Filtered Query: ${time5}ms (${filteredResults.length} records)`);

        // Test different page sizes
        console.log('\n📄 Testing Different Page Sizes...');
        const pageSizes = [5, 10, 25, 50];
        
        for (const pageSize of pageSizes) {
            const start = Date.now();
            const query = getLeadsQueryOptimized(testParams, userLocations, 0, pageSize);
            const results = await Lead.aggregate(query);
            const time = Date.now() - start;
            console.log(`   ✓ Page size ${pageSize}: ${time}ms (${results.length} records)`);
        }

        // Memory usage test
        console.log('\n💾 Memory Usage:');
        const memUsage = process.memoryUsage();
        console.log(`   RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
        console.log(`   Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
        console.log(`   Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);

        // Index usage check
        console.log('\n🗂️  Checking Index Usage...');
        const explainResult = await Lead.collection.aggregate([
            { $match: { createdAt: { $exists: true } } },
            { $sort: { createdAt: -1 } },
            { $limit: 10 }
        ]).explain('executionStats');

        const executionStats = explainResult.stages[0].$cursor.executionStats;
        console.log(`   ✓ Total docs examined: ${executionStats.totalDocsExamined}`);
        console.log(`   ✓ Total docs returned: ${executionStats.totalDocsReturned}`);
        console.log(`   ✓ Execution time: ${executionStats.executionTimeMillis}ms`);
        console.log(`   ✓ Index used: ${executionStats.executionStages.indexName || 'No index'}`);

        console.log('\n✅ Performance testing completed!');

    } catch (error) {
        console.error('❌ Error during performance testing:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

// Run the test
if (require.main === module) {
    testLeadPerformance();
}

module.exports = testLeadPerformance;
