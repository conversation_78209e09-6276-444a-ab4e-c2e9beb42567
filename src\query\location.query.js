const { default: mongoose } = require("mongoose");
const { ServiceStatus, combinedMap } = require("../constant/enums");

const getLocationQuery = (params, userLocations) => [
    {
        $match: {
            ...(params.location_Id ? { _id: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
                _id: {
                    $in: userLocations.map(id => new mongoose.Types.ObjectId(id)),
                },
            }
                : {}),
            ...(params.status ? { isActive: params.status === 'true' ? true : false } : {}),
            ...(params.dateFrom && params.dateEnd
                ? {
                    createdAt: {
                        $gte: new Date(params.dateFrom),
                        $lte: new Date(params.dateEnd),
                    }
                }
                : params.dateFrom
                    ? {
                        createdAt: {
                            $gte: new Date(params.dateFrom),
                        }
                    }
                    : {})
        }
    },
    {
        $project: {
            _id: 1,
            location: 1,
            meevo_location_id: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1
        }
    }
];

const locationSearchFields = [
    '_id',
    'location',
    'meevo_location_id',
    'isActive',
    'createdAt',
    'updatedAt'
];

module.exports = {
    getLocationQuery,
    locationSearchFields
}