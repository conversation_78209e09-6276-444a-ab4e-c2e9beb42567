const mongoose = require('mongoose');

const meevoClientSchema = new mongoose.Schema({
    client_id: {
        type: String,
        required: true
    },
    first: {
        type: String,
        required: true
    },
    last: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    phone: {
        type: String,
        default: null
    },
    tenant_id: {
        type: String,
        required: true
    },
    meevo_location_id: {
        type: String,
        required: true
    },
    created_on: {
        type: Date
    },
    location_Id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Location'
    }
}, { timestamps: true });

const MeevoClient = mongoose.model('MeevoClient', meevoClientSchema);
module.exports = MeevoClient

