const { AppError } = require('../util/index');
const AppUser = require('../model/appUser');
const mongoose = require('mongoose');
const getResourceById = require('../util/resourceById');
const { APP_USER, NOPERMISSION } = require('../constant/enums');

class PermissionMiddleware {
    static checkPermission(moduleName) {
        return async (req, res, next) => {
            try {
                const userId = req.id;
                if (!moduleName) throw new AppError(400, 'Module name is required');

                const [user] = await AppUser.aggregate([
                    {
                        $match: {
                            _id: new mongoose.Types.ObjectId(userId)
                        }
                    },
                    {
                        $lookup: {
                            from: 'permissions',
                            localField: "_id",
                            foreignField: 'userId',
                            as: 'permissions',
                            pipeline: [
                                {
                                    $match: {
                                        isDeleted: false
                                    }
                                }
                            ]
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            isEnable: 1,
                            permissions: 1
                        }
                    }
                ]);

                if (!user) throw new AppError(404, 'User not found');
                if (!user.isEnable) throw new AppError(403, 'User account is disabled. Contact admin.');

                const method = req.method.toUpperCase();
                const permissions = user.permissions;
                const action = method === 'GET' ? 'view' : 'edit';
                if (moduleName === NOPERMISSION) {
                    req.userLocations = permissions.map(p => p.locationId?.toString());
                    return next();
                }
                const allowedLocations = permissions
                    .filter(p => p.permissions?.[moduleName]?.[action] === true)
                    .map(p => p.locationId?.toString());

                req.userLocations = allowedLocations.map(id => new mongoose.Types.ObjectId(id));;

                if (!allowedLocations.length) {
                    throw new AppError(403, `You don't have permission to ${action} ${moduleName}`);
                }

                const resourceId = req.params?.id;

                if (resourceId) {
                    const resource = await getResourceById(resourceId, moduleName);
                    if (!resource) {
                        throw new AppError(404, `${moduleName} not found`);
                    }

                    const hasAccess = moduleName === APP_USER
                        ? resource.some(id => allowedLocations.includes(id.toString()))
                        : allowedLocations.includes(resource.toString());

                    if (!hasAccess) {
                        throw new AppError(403, `You don't have access to this ${moduleName}`);
                    }
                }

                next();
            } catch (error) {
                next(error);
            }
        };
    }
}

module.exports = PermissionMiddleware;
