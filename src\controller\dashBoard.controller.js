const DashBoardService = require('../service/dahsBoard.service');

class DashBoardController {
    static async getDashBoardDetails(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const data = await DashBoardService.getLeadDashboardData(queryParams, locations);
            res.status(200).json(data);
        } catch (error) {
            next(error);
        }
    }

    static async leaderBoard(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const data = await DashBoardService.getLeaderBoard(queryParams, locations);
            res.status(200).json(data);
        } catch (error) {
            next(error);
        }
    }
     static async leadBooking(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const data = await DashBoardService.getleadbooking(queryParams, locations);
            res.status(200).json(data);
        } catch (error) {
            next(error);
        }
    }

    static async exportLeaderBoard(
        req, res, next
    ) {
        try {
            const locations = req.userLocations;
            const queryParams = req.query;
            const exportData = await DashBoardService.exportLeaderBoardData(queryParams, locations);
            const dataBuffer = Buffer.from(exportData.data, 'base64');
            let fileName = `exported_Leader_Board_${Date.now()}.csv`;
            let contentType = 'text/csv';

            res
                .setHeader('Content-Disposition', `attachment; filename=${fileName}`)
                .setHeader('Content-Type', contentType)
                .send(dataBuffer.toString('utf8'));
        } catch (error) {
            next(error);
        }
    }

}

module.exports = DashBoardController