const { status } = require('express/lib/response');
const CallerHistory = require('../model/callerHistory');
const Lead = require('../model/lead');
const { AppError } = require('../util/index');
const mongoose = require('mongoose');
const { ServiceStatus } = require('../constant/enums');
const handleServiceError = require('../util/handleError');
const AppUser = require('../model/appUser');

class CallerService {
    static async createCallerdataHistory(data, userId) {
        try {
            const status = true;
            const message = 'Record created successfully'
            const { leadId, reason, notes, service_status } = data
            const lead = await Lead.findOne({ _id: new mongoose.Types.ObjectId(leadId) })
            if (!lead)
                throw new AppError(404, 'Lead not found');
            lead.service_status = service_status;
            lead.callerId = userId;
            lead.save()

            const callerHistory = await CallerHistory.create({
                userId: new mongoose.Types.ObjectId(userId),
                reason,
                notes,
                leadId: new mongoose.Types.ObjectId(leadId),
                action: service_status
            })
            if (!callerHistory)
                throw new AppError(403, 'Request Failed')
            const user = await AppUser.findOne({ _id: callerHistory.userId });
            return {
                status,
                message,
                data: {
                    caller: `${user.firstName} ${user.lastName}`,
                    leadId: callerHistory.leadId,
                    reason: callerHistory.reason,
                    notes: callerHistory.notes,
                    userId: callerHistory.userId,
                    action: callerHistory.action,
                    createdAt: callerHistory.createdAt,
                    _id: callerHistory._id,
                }
            }
        } catch (error) {
            handleServiceError(error)
        }
    }
}

module.exports = CallerService