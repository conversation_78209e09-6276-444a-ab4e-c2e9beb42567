const config = require('config');
//const AppUser = require('../model/appUser');
const { AppError } = require('../util/index');
const GhlPush = require('../model/ghlPush');
const mongoose = require('mongoose');
const Customer = require('../model/customer');
const Permission = require('../model/permissions');
const { DEFAULT_PASSWORD } = require('../constant/enums');
const { getAllUsers, userSearchFeilds } = require('../query/appUser.query');
const { baseListQuery } = require('../query/baseList.query');
const { getGHLDataQuery, getGHLDataQueryOptimized, getGHLDataQueryLightweight, getGHLDataCountQuery, ghlserchfields } = require('../query/ghlPush.query');
const AppUser = require('../model/appUser');
const handleServiceError = require('../util/handleError');

class GhlPushService {
    static async getGHLS(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';

            // Parse pagination parameters
            const pageNum = params.pageNum ? parseInt(params.pageNum) : 1;
            const limit = params.pageLimit ? parseInt(params.pageLimit) : 10;
            const skip = limit * (pageNum - 1);

            // Choose query based on performance requirements
            // Use lightweight query if location details are not critical (fastest)
            // Use optimized query for full data with better performance than original
            const useLight = params.lightweight === 'true' || params.lightweight === true;

            // GHL data is queried directly from customers collection
            const dataQuery = useLight
                ? getGHLDataQueryLightweight(params, userLocations, skip, limit)
                : getGHLDataQueryOptimized(params, userLocations, skip, limit);
            const countQuery = getGHLDataCountQuery(params, userLocations);

            // Execute both queries in parallel on Customer collection
            const [ghldatass, count] = await Promise.all([
                Customer.aggregate(dataQuery),
                Customer.aggregate(countQuery),
            ]);

            const totalCount = count[0]?.totalCount || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';

            return {
                status,
                data: ghldatass,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }


}

module.exports = GhlPushService;
