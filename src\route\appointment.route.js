const express = require('express');
const { AppointmentController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { APPOINTMENT } = require('../constant/enums');

class AppointmentRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(APPOINTMENT), AppointmentController.getAppointments);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = AppointmentRoute;
