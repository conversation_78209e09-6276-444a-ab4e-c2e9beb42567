const express = require('express');
const { LeadsController } = require('../controller/index');
const { Authentication } = require('../Middleware/index');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { LEADS, EXPORT, NOPERMISSION } = require('../constant/enums');

class LeadsRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(LEADS), LeadsController.getLeads);
        this.router.post('/create-web-lead', LeadsController.createLead)
        this.router.get('/export', Authentication.authUser, PermissionMiddleware.checkPermission(NOPERMISSION), LeadsController.exportLeadData)
    }

    getRoute() {
        return this.router;
    }
}

module.exports = LeadsRoute;
