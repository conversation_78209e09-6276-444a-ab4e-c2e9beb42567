const mongoose = require('mongoose');
const { SERVICE_STATUS } = require('../constant/enums');
const UTMSchema = new mongoose.Schema({
    UTM_source: { type: String },
    UTM_medium: { type: String },
    UTM_campaign: { type: String },
    gclid: { type: String },
    source: { type: String }
})
const leadSchema = new mongoose.Schema({
    user_id: { type: String },
    firstname: { type: String },
    lastname: { type: String },
    email: { type: String },
    phone: { type: String },
    tag_id: {
        type: [Number],
        default: []
    },
    location: {
        type: String
    },
    source: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Source'
    },
    callerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AppUser'
    },
    position: { type: String },
    emma_push: { type: Number, default: 0 },
    zen_push: {
        type: Boolean,
        default: false
    },
    is_matched: {
        type: Boolean,
        default: false
    },
    meevo_client: { type: String },
    location_Id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Location'
    },
    location_Id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Location" // if you want to populate later
        //required: true
    },
    service_status: {
        type: String,
        enum: SERVICE_STATUS,
        default: 'NEEDS_ACTION'
    },
    UTM_details: {
        type: UTMSchema
    },
    business_owner: {
        type: Boolean,
        default: false
    }
}, { timestamps: true });

// Add indexes for better query performance
leadSchema.index({ location_Id: 1, createdAt: -1 }); // Compound index for location filtering and sorting
leadSchema.index({ service_status: 1 }); // Index for service status filtering
leadSchema.index({ source: 1 }); // Index for source filtering
leadSchema.index({ tag_id: 1 }); // Index for tag filtering
leadSchema.index({ createdAt: -1 }); // Index for date sorting
leadSchema.index({ firstname: 1 }); // Index for name sorting
leadSchema.index({ lastname: 1 }); // Index for name sorting
leadSchema.index({ email: 1 }); // Index for email sorting/searching
leadSchema.index({ phone: 1 }); // Index for phone searching

const Lead = mongoose.model('Lead', leadSchema);
module.exports = Lead
