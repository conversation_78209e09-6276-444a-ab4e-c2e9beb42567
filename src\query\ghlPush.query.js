// const { default: mongoose } = require("mongoose");

// const getGHLDataQuery = (params, userLocations) => [

//     {
//                 $match: {
//                   ghl_push: true,
//                   ...(params.email ? { email: params.email } : {}) // <- filter by email if provided
//                 }
//               },
//     {
//         $lookup: {
//             from: "customers",
//             localField: "customer_id",
//             foreignField: "customer_id",
//             pipeline: [
//                 {

//                     $match: {
//                         ...(params.location_Id ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
//                             location_Id: {
//                                 $in: userLocations
//                             }
//                         }
//                             : {}),
//                         ...(params.YTDmin && params.YTDmax
//                             ? {

//                                 "total_expense.total": {
//                                     $gte: Number(params.YTDmin),
//                                     $lte: Number(params.YTDmax)
//                                 },

//                             }
//                             : {}),
//                         ...(params.LifeTimeTotalMin && params.LifeTimeTotalMax
//                             ? {
//                                 "total_expense.life_total": {
//                                     $gte: Number(params.LifeTimeTotalMin),
//                                     $lte: Number(params.LifeTimeTotalMax)
//                                 },
//                             }
//                             : {}),
//                     }
//                 },
//                 {
//                     $lookup: {
//                         from: "locations",
//                         localField: "location_Id",
//                         foreignField: "_id",
//                         as: "location"
//                     }
//                 },
//                 {
//                     $unwind: {
//                         path: "$location"
//                     }
//                 }
//             ],
//             as: "customerDetail"
//         }
//     },
//     {
//         $unwind: {
//             path: "$customerDetail"
//         }
//     },
//     {
//         $project: {
//             _id: 1,
//             first: '$customerDetail.first',
//             last: '$customerDetail.last',
//             email: '$customerDetail.email',
//             phone: '$customerDetail.phone',
//             club: '$customerDetail.location.location',
//             total_expense: '$customerDetail.total_expense'
//         }
//     }
// ];

// const ghlserchfields = [
//     '_id',
//     'first',
//     'last',
//     'email',
//     'phone',
//     'club',
//     'total_expense'
// ];

// module.exports = {
//     getGHLDataQuery,
//     ghlserchfields
// }

const { default: mongoose } = require("mongoose");

// Optimized GHL query with early pagination and better performance
const getGHLDataQueryOptimized = (params, userLocations, skip = 0, limit = 100) => {
  const sortOrder = params.sortOrder?.toLowerCase() === 'desc' ? -1 : 1;
  const allowedSortFields = ['first', 'last', 'email', 'createdAt', '_id'];
  const sortField = allowedSortFields.includes(params.sortField) ? params.sortField : '_id';

  return [
    // Step 1: Filter customers early with all conditions
    {
      $match: {
        ghl_push: true, // Primary filter
        ...(params.location_Id
          ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
          : userLocations?.length
            ? { location_Id: { $in: userLocations } }
            : {}
        ),
        ...(params.YTDmin && params.YTDmax
          ? { "total_expense.total": { $gte: Number(params.YTDmin), $lte: Number(params.YTDmax) } }
          : {}
        ),
        ...(params.gPlusBadge == "true"
          ? { "total_expense.total": { $ne: 0 } }
          : params.gPlusBadge == "false"
            ? { "total_expense.total": 0 }
            : {}
        ),
        ...(params.email
          ? {
            $or: [
              { first: { $regex: params.email, $options: "i" } },
              { last: { $regex: params.email, $options: "i" } },
              { email: { $regex: params.email, $options: "i" } }
            ]
          }
          : {})
      }
    },
    // Step 2: Sort early to take advantage of indexes
    { $sort: { [sortField]: sortOrder } },
    // Step 3: Apply pagination early to reduce data processing
    { $skip: skip },
    { $limit: limit },
    // Step 4: Now do location lookup only on the paginated results
    {
      $lookup: {
        from: "locations",
        localField: "location_Id",
        foreignField: "_id",
        as: "location",
        pipeline: [{ $project: { location: 1 } }] // Only get location name
      }
    },
    { $unwind: { path: "$location", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        first: 1,
        last: 1,
        email: 1,
        phone: 1,
        club: "$location.location",
        total_expense: 1
      }
    }
  ];
};

// Original query kept for backward compatibility
const getGHLDataQuery = (params, userLocations, skip = 0, limit = 100) => [
  {
    $match: {
      ghl_push: true,
      ...(params.email
        ? {
          $or: [
            { first: { $regex: params.email, $options: "i" } },
            { last: { $regex: params.email, $options: "i" } },
            { email: { $regex: params.email, $options: "i" } }
          ]
        }
        : {})
    }
  },

  {
    $lookup: {
      from: "customers",
      localField: "customer_id",
      foreignField: "customer_id",
      pipeline: [
        {
          $match: {
            ...(params.location_Id
              ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
              : userLocations?.length
                ? { location_Id: { $in: userLocations } }
                : {}
            ),
            ...(params.YTDmin && params.YTDmax
              ? { "total_expense.total": { $gte: Number(params.YTDmin), $lte: Number(params.YTDmax) } }
              : {}
            ),
            ...(params.gPlusBadge == "true"
              ? { "total_expense.total": { $ne: 0 } }
              : params.gPlusBadge == "false"
                ? { "total_expense.total": 0 }
                : {}
            )
          }
        },
        { $lookup: { from: "locations", localField: "location_Id", foreignField: "_id", as: "location" } },
        { $unwind: { path: "$location", preserveNullAndEmptyArrays: true } }
      ],
      as: "customerDetail"
    }
  },
  { $unwind: "$customerDetail" }, // This removes docs without matching customers
  {
    $project: {
      _id: 1,
      first: "$customerDetail.first",
      last: "$customerDetail.last",
      email: "$customerDetail.email",
      phone: "$customerDetail.phone",
      club: "$customerDetail.location.location",
      total_expense: "$customerDetail.total_expense"
    }
  },
  { $sort: { _id: -1 } },
  { $skip: skip },   // ✅ Move pagination after lookups
  { $limit: limit }  // ✅ Move pagination after lookups
];



// Ultra-lightweight GHL query for maximum performance - no location lookup
const getGHLDataQueryLightweight = (params, userLocations, skip = 0, limit = 100) => {
  const sortOrder = params.sortOrder?.toLowerCase() === 'desc' ? -1 : 1;
  const allowedSortFields = ['first', 'last', 'email', 'createdAt', '_id'];
  const sortField = allowedSortFields.includes(params.sortField) ? params.sortField : '_id';

  return [
    {
      $match: {
        ghl_push: true,
        ...(params.location_Id
          ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
          : userLocations?.length
            ? { location_Id: { $in: userLocations } }
            : {}
        ),
        ...(params.YTDmin && params.YTDmax
          ? { "total_expense.total": { $gte: Number(params.YTDmin), $lte: Number(params.YTDmax) } }
          : {}
        ),
        ...(params.gPlusBadge == "true"
          ? { "total_expense.total": { $ne: 0 } }
          : params.gPlusBadge == "false"
            ? { "total_expense.total": 0 }
            : {}
        ),
        ...(params.email
          ? {
            $or: [
              { first: { $regex: params.email, $options: "i" } },
              { last: { $regex: params.email, $options: "i" } },
              { email: { $regex: params.email, $options: "i" } }
            ]
          }
          : {})
      }
    },
    { $sort: { [sortField]: sortOrder } },
    { $skip: skip },
    { $limit: limit },
    {
      $project: {
        _id: 1,
        first: 1,
        last: 1,
        email: 1,
        phone: 1,
        club: "Location Name", // Placeholder - no location lookup for speed
        total_expense: 1
      }
    }
  ];
};

// Optimized count query for GHL data - direct count on customers collection
const getGHLDataCountQuery = (params, userLocations) => [
  {
    $match: {
      ghl_push: true,
      ...(params.location_Id
        ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
        : userLocations?.length
          ? { location_Id: { $in: userLocations } }
          : {}
      ),
      ...(params.YTDmin && params.YTDmax
        ? { "total_expense.total": { $gte: Number(params.YTDmin), $lte: Number(params.YTDmax) } }
        : {}
      ),
      ...(params.gPlusBadge == "true"
        ? { "total_expense.total": { $ne: 0 } }
        : params.gPlusBadge == "false"
          ? { "total_expense.total": 0 }
          : {}
      ),
      ...(params.email
        ? {
          $or: [
            { first: { $regex: params.email, $options: "i" } },
            { last: { $regex: params.email, $options: "i" } },
            { email: { $regex: params.email, $options: "i" } }
          ]
        }
        : {})
    }
  },
  {
    $count: "totalCount"
  }
];

const ghlserchfields = [
  "_id",
  "first",
  "last",
  "email",
  "phone",
  "club",
  "total_expense"
];

module.exports = {
  getGHLDataQuery,
  getGHLDataQueryOptimized,
  getGHLDataQueryLightweight,
  getGHLDataCountQuery,
  ghlserchfields
};
