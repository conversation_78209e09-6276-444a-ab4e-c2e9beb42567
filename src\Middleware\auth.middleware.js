const jwt = require('jsonwebtoken');
const config = require('config');
const {AppError} = require('../util/index');

class Authentication {
    static async authUser(req, res, next) {
        try {
            const authHeader = req.headers['authorization'];
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                throw new AppError(401, 'User is not Authorized');
            }
    
            const token = authHeader.split(' ')[1];
            if (!token) {
                throw new AppError(401, 'Token not found');
            }
    
            const SECRET_KEY = config.get('JWT_SECRET_KEY');
            if (!SECRET_KEY) {
                throw new AppError(500, 'Server config error: JWT secret missing');
            }
    
            const decoded = jwt.verify(token, SECRET_KEY);
            req.id = decoded.id;
    
            next();
        } catch (error) {
            next(error);
        }
    }
}

module.exports = Authentication;
