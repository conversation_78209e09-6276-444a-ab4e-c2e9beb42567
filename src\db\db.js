const config = require('config')
const mongoose = require('mongoose');

const connectDB = async () => {
    const MONGODB_URI = config.get('DB_URL')
    if (!MONGODB_URI) {
        console.error('MONGODB_URI is not defined');
        return;
    }

    try {
        await mongoose.connect(MONGODB_URI);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
    }
}

module.exports = connectDB;
