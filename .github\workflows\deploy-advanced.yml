name: Advanced Deploy to VPS

on:
  push:
    branches:
      - main
      - master
    paths-ignore:
      - '**.md'
      - '.gitignore'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'tgp'
        type: choice
        options:
          - tgp
          - demo
          - wp
      skip_tests:
        description: 'Skip tests'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  DEPLOY_PATH: '~/better_csi/api_service/Better_CSI_MS'

jobs:
  test:
    runs-on: ubuntu-latest
    if: ${{ !github.event.inputs.skip_tests }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting (if configured)
      run: npm run lint || echo "No linting configured"

    - name: Run tests
      run: npm test || echo "No tests configured"

  deploy:
    runs-on: ubuntu-latest
    needs: [test]
    if: always() && (needs.test.result == 'success' || needs.test.result == 'skipped')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set environment
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          echo "DEPLOY_ENV=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
        else
          echo "DEPLOY_ENV=tgp" >> $GITHUB_ENV
        fi

    - name: Create deployment backup and deploy
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        command_timeout: 10m
        script: |
          set -e
          
          echo "🚀 Starting deployment for environment: ${{ env.DEPLOY_ENV }}"
          
          # Navigate to project directory
          cd ${{ env.DEPLOY_PATH }}
          
          # Create backup of current version
          BACKUP_DIR="~/backups/better_csi_ms_$(date +%Y%m%d_%H%M%S)"
          mkdir -p ~/backups
          cp -r . $BACKUP_DIR
          echo "📦 Backup created at: $BACKUP_DIR"
          
          # Get current commit hash for rollback reference
          CURRENT_COMMIT=$(git rev-parse HEAD)
          echo "📝 Current commit: $CURRENT_COMMIT"
          
          # Pull latest changes
          echo "⬇️ Pulling latest changes..."
          git fetch origin
          git reset --hard origin/main || git reset --hard origin/master
          
          # Install dependencies
          echo "📦 Installing dependencies..."
          npm install --production --silent
          
          # Stop current service
          echo "⏹️ Stopping current service..."
          pm2 stop api-${{ env.DEPLOY_ENV }} || echo "Service not running"
          
          # Start service with appropriate environment
          echo "▶️ Starting service for environment: ${{ env.DEPLOY_ENV }}"
          case "${{ env.DEPLOY_ENV }}" in
            "tgp")
              pm2 start npm --name api-tgp -- run tgp_start
              ;;
            "demo")
              pm2 start npm --name api-demo -- run demo_start
              ;;
            "wp")
              pm2 start npm --name api-wp -- run wp_start
              ;;
            *)
              echo "❌ Unknown environment: ${{ env.DEPLOY_ENV }}"
              exit 1
              ;;
          esac
          
          # Save PM2 configuration
          pm2 save
          
          echo "✅ Deployment completed!"

    - name: Health check and verification
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          echo "🔍 Performing health check..."
          
          # Wait for service to start
          sleep 15
          
          # Check if service is running
          if pm2 list | grep -q "api-${{ env.DEPLOY_ENV }}.*online"; then
            echo "✅ Service api-${{ env.DEPLOY_ENV }} is running!"
            
            # Show service details
            pm2 show api-${{ env.DEPLOY_ENV }}
            
            # Show recent logs
            echo "📋 Recent logs:"
            pm2 logs api-${{ env.DEPLOY_ENV }} --lines 10 --nostream
            
          else
            echo "❌ Service api-${{ env.DEPLOY_ENV }} is not running!"
            echo "📋 Error logs:"
            pm2 logs api-${{ env.DEPLOY_ENV }} --lines 50 --nostream
            exit 1
          fi

    - name: Cleanup old backups
      if: success()
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          # Keep only last 5 backups
          cd ~/backups
          ls -t | grep better_csi_ms_ | tail -n +6 | xargs -r rm -rf
          echo "🧹 Cleaned up old backups"

  rollback:
    runs-on: ubuntu-latest
    if: failure() && needs.deploy.result == 'failure'
    needs: [deploy]
    
    steps:
    - name: Rollback deployment
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          echo "🔄 Rolling back deployment..."
          
          # Get the latest backup
          LATEST_BACKUP=$(ls -t ~/backups/better_csi_ms_* | head -n 1)
          
          if [ -n "$LATEST_BACKUP" ]; then
            echo "📦 Restoring from backup: $LATEST_BACKUP"
            
            # Stop current service
            pm2 stop api-${{ env.DEPLOY_ENV }} || echo "Service not running"
            
            # Restore from backup
            cd ${{ env.DEPLOY_PATH }}
            rm -rf ./*
            cp -r $LATEST_BACKUP/* .
            
            # Restart service
            case "${{ env.DEPLOY_ENV }}" in
              "tgp")
                pm2 start npm --name api-tgp -- run tgp_start
                ;;
              "demo")
                pm2 start npm --name api-demo -- run demo_start
                ;;
              "wp")
                pm2 start npm --name api-wp -- run wp_start
                ;;
            esac
            
            pm2 save
            echo "✅ Rollback completed!"
          else
            echo "❌ No backup found for rollback!"
          fi
