name: Deploy to VPS

on:
  push:
    branches:
      - main
      - master
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'tgp'
        type: choice
        options:
          - tgp
          - demo
          - wp

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests (if any)
      run: npm test || echo "No tests found"

    - name: Deploy to VPS
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          # Navigate to the project directory
          cd ~/better_csi/api_service/Better_CSI_MS
          
          # Pull latest changes
          git pull origin main || git pull origin master
          
          # Install/update dependencies
          npm install --production
          
          # Determine which service to restart based on environment
          ENV="${{ github.event.inputs.environment || 'tgp' }}"
          
          # Stop the specific PM2 process
          pm2 stop api-$ENV || echo "Process api-$ENV not running"
          
          # Start the service with the appropriate environment
          case $ENV in
            "tgp")
              pm2 start npm --name api-tgp -- run tgp_start
              ;;
            "demo")
              pm2 start npm --name api-demo -- run demo_start
              ;;
            "wp")
              pm2 start npm --name api-wp -- run wp_start
              ;;
            *)
              echo "Unknown environment: $ENV"
              exit 1
              ;;
          esac
          
          # Save PM2 configuration
          pm2 save
          
          # Show PM2 status
          pm2 status

    - name: Verify deployment
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USERNAME }}
        password: ${{ secrets.VPS_PASSWORD }}
        port: 22
        script: |
          # Wait a moment for the service to start
          sleep 10
          
          # Check if the service is running
          ENV="${{ github.event.inputs.environment || 'tgp' }}"
          if pm2 list | grep -q "api-$ENV.*online"; then
            echo "✅ Deployment successful! Service api-$ENV is running."
            pm2 show api-$ENV
          else
            echo "❌ Deployment failed! Service api-$ENV is not running."
            pm2 logs api-$ENV --lines 50
            exit 1
          fi

    - name: Notify deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "🎉 Deployment completed successfully!"
        else
          echo "💥 Deployment failed!"
        fi
