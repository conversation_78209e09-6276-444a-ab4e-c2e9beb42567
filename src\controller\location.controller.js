const LocationService = require('../service/location.service');

class LocationController {
    static async getLocation(req, res, next) {
        try {
            const queryParams = req.query;
            const locations = req.userLocations;
            const location = await LocationService.getLocations(queryParams, locations);
            res.status(200).json(location);
        } catch (error) {
            next(error);
        }
    }

    static async getMeevoLocation(req, res, next) {
        try {
            const id = req.params.meevoLocationId;
            const location = await LocationService.getLocationByMeevoLoctionId(id);
            res.status(200).json(location);
        } catch (error) {
            next(error);
        }
    }

    static async getLocationById(req, res, next) {
        try {
            const id = req.params.id;
            const location = await LocationService.getLocationById(id);
            res.status(200).json(location);
        } catch (error) {
            next(error);
        }
    }

    static async createLocation(req, res, next) {
        try {
            const data = req.body;
            const country = await LocationService.createLocation(data);
            res.status(200).json(country);
        } catch (error) {
            next(error);
        }
    }

    static async updateLocation(req, res, next) {
        try {
            const id = req.params.id;
            const data = req.body;
            const location = await LocationService.updateLocation(id, data);
            res.status(200).json(location);
        } catch (error) {
            next(error);
        }
    }
}

module.exports = LocationController