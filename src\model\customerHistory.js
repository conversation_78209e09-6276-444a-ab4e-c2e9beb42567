const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const customerHistorySchema = new Schema({
    customer_id: { type: String, required: true, ref: 'meevoclient' }, // Reference to MeevoClient.client_id
    meevo_location: { type: String, required: true },
    first: { type: String, required: true },
    last: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, default: null },
    club: { type: String, default: null },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations', required: true },

    service: { type: Number, default: 0 },
    membership: { type: Number, default: 0 },
    product: { type: Number, default: 0 },
    gift: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    life_total: { type: Number, default: 0 },
    tgs_total: { type: Number, default: 0 },
    tgs_life_total: { type: Number, default: 0 },
    pushed: { type: Boolean, default: false },
}, {
    timestamps: true
});

const CustomerHistory = mongoose.model('customerhistory', customerHistorySchema);

module.exports = CustomerHistory;