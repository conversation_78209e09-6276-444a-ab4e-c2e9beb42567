const { TRANSACTION, GHL, LEADS, APP_USER, AD_MANAGER, LOCATION } = require('../constant/enums');
const AdManager = require('../model/adManager');
const AppUser = require('../model/appUser');
const GhlPush = require('../model/ghlPush');
const Lead = require('../model/lead');
const Location = require('../model/location');
const mongoose = require('mongoose');
const { AppError } = require('./index');

async function getResourceById(id, moduleName) {
    const objectId = new mongoose.Types.ObjectId(id);

    switch (moduleName) {
        case TRANSACTION:
            return null; // or throw new AppError(400, 'Transactions do not support resource check');

        case GHL: {
            const result = await GhlPush.aggregate([
                { $match: { _id: objectId } },
                {
                    $lookup: {
                        from: 'customers',
                        localField: 'customer_id',
                        foreignField: 'customer_id',
                        as: 'customer'
                    }
                },
                { $unwind: '$customer' },
                {
                    $project: {
                        locationId: '$customer.location_Id'
                    }
                }
            ]);

            return result?.[0]?.locationId || null;
        }

        case LEADS: {
            const lead = await Lead.findById(objectId).select('location_Id').lean();
            return lead?.location_Id || null;
        }

        case APP_USER: {
            const user = await AppUser.findById(objectId).select('locations').lean();
            return user?.locations || null;
        }

        case AD_MANAGER: {
            const manager = await AdManager.findById(objectId).select('locationId').lean();
            return manager?.locationId || null;
        }

        case LOCATION: {
            const location = await Location.findById(objectId).select('_id').lean();
            return location?._id || null;
        }

        default:
            throw new AppError(500, `No model mapping defined for module "${moduleName}"`);
    }
}

module.exports = getResourceById;
