const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const meevoAppointmentSchema = new Schema({
    appointment_id: { type: String, required: true, unique: true },
    appointment_service_id: { type: String }, // Ref to MeevoSale
    book_method: { type: String },
    book_type: { type: String },
    customer_id: { type: String, required: true }, // Ref to MeevoClient.client_id
    tenant_id: { type: String },
    meevo_location: { type: String },
    status: { type: String },
    created_on: { type: Date },
    start_on: { type: Date },
    email: { type: String },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations' },
    status_txt: { type: String },
    book_type_txt: { type: String },
    book_method_txt: { type: String },
    sourceId: { type: mongoose.Types.ObjectId, ref: 'sources' }
}, {
    timestamps: true
});

const MeevoAppointment = mongoose.model('meevoAppointment', meevoAppointmentSchema);

module.exports = MeevoAppointment;