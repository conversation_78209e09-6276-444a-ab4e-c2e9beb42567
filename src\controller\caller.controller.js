const CallerService = require('../service/caller.service');
const { AppError } = require('../util/index');
const mongoose = require('mongoose');

class CallerController {
    static async createCallerHistory(req, res, next) {
        try {
            const data = req.body;
            const id = req.id;
            const callerHistory = await CallerService.createCallerdataHistory(data, id)
            res.status(200).json(callerHistory);
        } catch (error) {
            next(error)
        }
    }
}

module.exports = CallerController