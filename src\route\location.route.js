const express = require('express');
const { Authentication } = require('../Middleware/index');
const LocationController = require('../controller/location.controller');
const PermissionMiddleware = require('../Middleware/permission.middleware');
const { LOCATION } = require('../constant/enums');

class LocationRoute {
    constructor() {
        this.router = express.Router();
        this.routes();
    }

    routes() {
        this.router.get('/', Authentication.authUser, PermissionMiddleware.checkPermission(LOCATION), LocationController.getLocation);
        this.router.post('/', Authentication.authUser, PermissionMiddleware.checkPermission(LOCATION), LocationController.createLocation);
        this.router.get('/:meevoLocationId', Authentication.authUser, LocationController.getMeevoLocation);
        this.router.get('/detail/:id', Authentication.authUser, PermissionMiddleware.checkPermission(LOCATION), LocationController.getLocationById);
        this.router.put('/detail/:id', Authentication.authUser, PermissionMiddleware.checkPermission(LOCATION), LocationController.updateLocation);
    }

    getRoute() {
        return this.router;
    }
}

module.exports = LocationRoute;
