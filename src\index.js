const express = require('express');
const connectDB = require('./db/db');
const { errorHandlerMiddleware } = require('./Middleware/index');
const cors = require('cors');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const path = require('path');
const config = require('config');
const UserRoute = require('./route/user.routes');
const LeadsRoute = require('./route/leads.routes');
const CallerRoute = require('./route/caller.routes');
const GhlPushRoute = require('./route/ghlPush.routes');
const AppointmentRoute = require('./route/appointment.route');
const { AdManagerRoute } = require('./route');
const LocationRoute = require('./route/location.route');
const TransactionRoute = require('./route/transaction.route');
const DashBoardRoute = require('./route/dashBoard.route');
class App {
    constructor() {
        this.app = express()
        this.config()
        this.connectDb()
        this.routes()
    }

    config() {
        this.app.use(cors({
            credentials: true,
            origin: true,
        }))
        this.app.use(cookieParser())
        this.app.use(express.json())
        this.app.use(bodyParser.json())
        this.app.use('/public', express.static(path.resolve(__dirname, '../public')))
    }

    async connectDb() {
        await connectDB()
    }

    routes() {
        const BASE_URL = config.get('BASE_URL')
        const _userRoute = new UserRoute().getRoute()
        const _leadRoute = new LeadsRoute().getRoute()
        const _callerRoute = new CallerRoute().getRoute()
        const _ghlPush = new GhlPushRoute().getRoute()
        const _appointment = new AppointmentRoute().getRoute()
        const _adMangerRoute = new AdManagerRoute().getRoute()
        const _locationRoute = new LocationRoute().getRoute()
        const _transactionRoute = new TransactionRoute().getRoute()
        const _dashBoardRoute = new DashBoardRoute().getRoute()
        this.app.use(`/api/user`, _userRoute)
        this.app.use(`/api/lead`, _leadRoute)
        this.app.use(`/api/caller`, _callerRoute)
        this.app.use(`/api/ghlPush`, _ghlPush)
        this.app.use('/api/appointments', _appointment)
        this.app.use('/api/ad-manager', _adMangerRoute)
        this.app.use('/api/location', _locationRoute)
        this.app.use('/api/transaction', _transactionRoute)
        this.app.use('/api/dashboard', _dashBoardRoute)
    }

    start(port) {
        this.app.use(errorHandlerMiddleware)
        this.app.listen(port, () => {
            console.log('server started on port', port)
        })
    }
}

module.exports = { App }
