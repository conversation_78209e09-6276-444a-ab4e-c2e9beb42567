# GitHub Actions Deployment Guide

This repository includes automated deployment workflows for deploying the Better CSI MS service to your VPS server.

## 🚀 Available Workflows

### 1. Basic Deployment (`deploy.yml`)
- **Trigger**: Automatic on push to `main`/`master` branch
- **Manual Trigger**: Can be triggered manually with environment selection
- **Features**: Basic deployment with service restart

### 2. Advanced Deployment (`deploy-advanced.yml`)
- **Trigger**: Automatic on push to `main`/`master` branch (excluding markdown files)
- **Manual Trigger**: Can be triggered manually with environment selection and test skipping
- **Features**: 
  - Automated testing before deployment
  - Backup creation before deployment
  - Health checks after deployment
  - Automatic rollback on failure
  - Cleanup of old backups

### 3. Manual Rollback (`rollback.yml`)
- **Trigger**: Manual only
- **Features**: 
  - Rollback to previous backup
  - Option to specify backup timestamp
  - Verification after rollback

## 🔧 Setup Instructions

### Step 1: Configure GitHub Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions, and add these secrets:

```
VPS_HOST: ************
VPS_USERNAME: root
VPS_PASSWORD: LxXQ47lg
```

**⚠️ Security Note**: Consider using SSH keys instead of passwords for better security.

### Step 2: Ensure VPS Prerequisites

Make sure your VPS has:
- Git installed and repository cloned at `~/better_csi/api_service/Better_CSI_MS`
- Node.js and npm installed
- PM2 installed globally (`npm install -g pm2`)
- Proper permissions for the deployment user

### Step 3: Verify Repository Structure

Ensure your VPS repository is set up correctly:
```bash
cd ~/better_csi/api_service/Better_CSI_MS
git remote -v  # Should show your GitHub repository
```

## 📋 Usage

### Automatic Deployment
- Push code to `main` or `master` branch
- The workflow will automatically deploy to the `tgp` environment

### Manual Deployment
1. Go to GitHub → Actions → "Deploy to VPS" or "Advanced Deploy to VPS"
2. Click "Run workflow"
3. Select the environment (tgp, demo, wp)
4. Click "Run workflow"

### Manual Rollback
1. Go to GitHub → Actions → "Rollback Deployment"
2. Click "Run workflow"
3. Select the environment to rollback
4. Optionally specify a backup timestamp (format: YYYYMMDD_HHMMSS)
5. Click "Run workflow"

## 🔍 Monitoring

### Check Deployment Status
- View workflow runs in GitHub Actions tab
- Check PM2 status on VPS: `pm2 status`
- View logs: `pm2 logs api-tgp` (replace with your environment)

### Available PM2 Commands on VPS
```bash
# Check status of all services
pm2 status

# View logs for specific service
pm2 logs api-tgp
pm2 logs api-demo
pm2 logs api-wp

# Restart a service
pm2 restart api-tgp

# Stop a service
pm2 stop api-tgp

# Show detailed info about a service
pm2 show api-tgp
```

## 🛠️ Troubleshooting

### Common Issues

1. **Deployment fails with "Permission denied"**
   - Check VPS credentials in GitHub secrets
   - Ensure the deployment user has proper permissions

2. **Service fails to start after deployment**
   - Check PM2 logs: `pm2 logs api-[environment]`
   - Verify environment configuration files exist
   - Check if all dependencies are installed

3. **Git pull fails**
   - Ensure the repository is properly configured on VPS
   - Check if there are uncommitted changes: `git status`
   - Reset if needed: `git reset --hard HEAD`

### Manual Deployment Commands (if workflows fail)
```bash
# On VPS
cd ~/better_csi/api_service/Better_CSI_MS
git pull origin main
npm install --production
pm2 restart api-tgp  # or api-demo, api-wp
```

## 🔒 Security Recommendations

1. **Use SSH Keys**: Replace password authentication with SSH key authentication
2. **Limit Access**: Create a dedicated deployment user with limited permissions
3. **Environment Variables**: Store sensitive configuration in environment variables
4. **Firewall**: Ensure proper firewall rules are in place

### Setting up SSH Key Authentication (Recommended)

1. Generate SSH key pair on your local machine:
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

2. Copy public key to VPS:
   ```bash
   ssh-copy-id root@************
   ```

3. Update GitHub secrets:
   - Remove `VPS_PASSWORD`
   - Add `VPS_SSH_KEY` with the private key content

4. Update workflow files to use SSH key instead of password

## 📊 Backup Management

Backups are automatically created before each deployment and stored in `~/backups/` on the VPS.

- Backups are named with timestamp: `better_csi_ms_YYYYMMDD_HHMMSS`
- Only the last 5 backups are kept (older ones are automatically cleaned up)
- Manual backups can be created before major changes

### Manual Backup
```bash
# On VPS
BACKUP_DIR="~/backups/manual_backup_$(date +%Y%m%d_%H%M%S)"
cp -r ~/better_csi/api_service/Better_CSI_MS $BACKUP_DIR
echo "Backup created at: $BACKUP_DIR"
```

## 🎯 Next Steps

1. Test the deployment workflow with a small change
2. Set up monitoring and alerting for your services
3. Consider implementing health check endpoints in your application
4. Set up SSL certificates for production environments
5. Implement proper logging and log rotation
