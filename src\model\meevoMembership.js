const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const meevoMembershipSchema = new Schema({
    membership_id: { type: String, required: true, unique: true }, // Ref to MeevoSale.cservice
    name: { type: String, required: true },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations', required: true }, // Ref to Location
}, {
    timestamps: true
});

const MeevoMembership = mongoose.model('meevoMembership', meevoMembershipSchema);

module.exports = MeevoMembership;