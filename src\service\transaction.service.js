const { AppError } = require('../util/index');

class TransactionService {
    static async getTransaction(locations) {
        try {
            // const [transaction]=await transaction.aggregate([])
            const transaction = {
                totalLeadRevenue: {
                    leads: 1234,
                    spend: 1234
                },
                dayPassSubmission: {
                    leads: 1234,
                    spend: 1234
                },
                lead50offFeedVet: {
                    leads: 1234,
                    spend: 1234
                }
            }
            return {
                status: true,
                data: transaction,
                message: 'request completed successfully',
            }
        } catch (error) {
            throw new AppError(error)
        }
    }
};

module.exports = TransactionService