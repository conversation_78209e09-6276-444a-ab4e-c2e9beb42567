const ServiceStatus = {
    SPAM: "Spam",
    NEEDS_ACTION: "Needs Action",
    THIRD_LVM: "Third LVM",
    TWICE_LVM: "Twice LVM",
    ONCE_LVM: "Once LVM",
    SPOKE_TO_CALL_BACK: "spoke to/Call Back",
    SPOKE_TO_NOT_READY_TO_BOOK: "spoke to/not ready to book",
    BOOKED_3C: "Booked 3C",
    BOOKED_5C: "Booked 5C",
    BOOKED_7C: "Booked 7C",
    BOOKED_OTHER: "Booked Other",
    NOT_INTERESTED: "Not Interested",
    SENT_EMAIL: "Sent Email",
    WRONG_NUMBER: "Wrong Number",
    REPEAT: "Repeat"
}
const combinedMap = {
    FRONT_DESK: 'Front Desk',
    ONLINE: 'Online',
    WALKIN_KIOSK: 'Walkin Kiosk',
    ONLINE_WALKIN_WAIT_LIST: 'Online Walkin Wait List',
    BOOKING_AGENT: 'Booking Agent',
    STANDING_DEFINITION: 'Standing Definition',
    WAIT_LIST: 'Wait List',
    MANUALLY_ADDED_VIA_WAIT_LIST: 'Manually added via Wait List',
    MOBILE: 'Mobile',
    // appointment_type
    WALK_IN: 'Walk In',
    PREBOOKED: 'Prebooked',
    BOOKED: 'Booked',
    // status_txt
    CHECKED_OUT: 'Checked Out',
    CANCELLED: 'Cancelled',
    NONE: 'None',
    REPLACED: 'Replaced',
    REMOVED: 'Removed'
};

const SERVICE_STATUS = [
    "SPAM",
    "NEEDS_ACTION",
    "THIRD_LVM",
    "TWICE_LVM",
    "ONCE_LVM",
    "SPOKE_TO_CALL_BACK",
    "SPOKE_TO_NOT_READY_TO_BOOK",
    "BOOKED_3C",
    "BOOKED_5C",
    "BOOKED_7C",
    "BOOKED_OTHER",
    "NOT_INTERESTED",
    "SENT_EMAIL",
    "WRONG_NUMBER",
    "REPEAT"
];


const DEFAULT_PASSWORD = 'Better_CSI#123';

const LocationType = {
    STORE: 'Store',
    OFFICE: 'Office',
    EVENT_SPACE: 'Event Space',
    CAMPUS: 'Campus',
    WELLNESS_CENTER: 'Wellness Center',
    LIBRARY: 'Library'
}

const Region = {
    NORTH: 'North',
    SOUTH: 'South',
    EAST: 'East',
    WEST: 'West',
    CENTRAL: 'Central',
    NORTHEAST: 'Northeast',
    NORTHWEST: 'Northwest',
    SOUTHEAST: 'Southeast',
    SOUTHWEST: 'Southwest'
}

const AccessibilityFeature = {
    WHEELCHAIR_ACCESS: 'Wheelchair access',
    BRAILLE_SIGNAGE: 'Braille signage',
    ELEVATOR_ACCESS: 'Elevator Access',
    ACCESSIBLE_RESTROOM: 'Accessible Restroom',
    ANIMAL_FRIENDLY: 'Animal Friendly',
    ACCESSIBLE_PARKING: 'Accessible Parking'
}

const TRANSACTION = "transaction"
const GHL = "ghl"
const APPOINTMENT = "appointment"
const LEADS = "leadsOverview"
const AD_MANAGER = "adsManagement"
const APP_USER = "userManagement"
const LOCATION = "locationManagement"
const EXPORT = "exportData"
const NOPERMISSION = 'no_permission'
const LAST30DAYS = 'last30days'
const MTD = 'mtd'
const PREVMONTH = 'prevmonth'
const ALL = 'all'

module.exports = {
    SERVICE_STATUS,
    DEFAULT_PASSWORD,
    ServiceStatus,
    combinedMap,
    LocationType,
    AccessibilityFeature,
    Region,
    EXPORT,
    LOCATION,
    APP_USER,
    AD_MANAGER,
    LEADS,
    APPOINTMENT,
    GHL,
    TRANSACTION,
    ALL,
    PREVMONTH,
    MTD,
    LAST30DAYS,
    NOPERMISSION
}