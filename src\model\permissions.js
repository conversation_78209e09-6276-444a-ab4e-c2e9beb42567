const mongoose = require('mongoose');

const permissionSchema = new mongoose.Schema({
    superAdminId: {
        type: mongoose.Types.ObjectId,
        ref: 'appusers',
        required: true
    },
    userId: {
        type: mongoose.Types.ObjectId,
        ref: 'appusers',
        required: true
    },
    locationId: {
        type: mongoose.Types.ObjectId,
        ref: 'locations',
        default: null
    },
    permissions: {
        transaction: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        appointment: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        ghl: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        leadsOverview: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        adsManagement: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        userManagement: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        exportData: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        },
        locationManagement: {
            view: { type: Boolean, default: false },
            edit: { type: Boolean, default: false }
        }
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isGlobal: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

const Permission = mongoose.model('permission', permissionSchema);
module.exports = Permission;
