const config = require('config');
const AppUser = require('../model/appUser');
const { AppError, transporter } = require('../util/index');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const Permission = require('../model/permissions');
const { DEFAULT_PASSWORD } = require('../constant/enums');
const { getAllUsers, userSearchFeilds, getAppUserByIdQuery } = require('../query/appUser.query');
const { baseListQuery } = require('../query/baseList.query');
const Source = require('../model/source');
const Location = require('../model/location');
const handleServiceError = require('../util/handleError');
const { emailSender } = require('../util/nodeMailer');
const crypto = require('crypto');
const { encryptEmail, decryptEmail } = require('../util/encryption');

class UserService {
    static async signUp(userData) {
        try {
            const existingUser = await AppUser.findOne({ email: userData.email });
            if (existingUser) {
                throw new AppError(409, 'User already exists');
            }

            const hashedPassword = await bcrypt.hash(userData.password, 10);

            const newUser = new AppUser({
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                password: hashedPassword,
                phone: userData.phone,
                locations: userData.locations?.map(id => new mongoose.Types.ObjectId(id)) || [],
                description: userData.description || '',
                isEnable: userData.isEnable || false
            });

            await newUser.save();
            return { data: newUser, status: true, mesage: 'Record created successfully' };
        } catch (error) {

        }
    }

    static async login(loginData) {
        try {
            const user = await AppUser.findOne({ email: loginData.email });
            if (!user) {
                throw new AppError(404, "User Doesn't Exist");
            }

            const passwordMatch = await bcrypt.compare(loginData.password, user.password);
            if (!passwordMatch) {
                throw new AppError(401, 'Credentials are wrong');
            }
            const SECRET_KEY = config.get('JWT_SECRET_KEY')
            if (!SECRET_KEY) {
                throw new AppError(404, 'Secret key not found');
            }

            const token = jwt.sign({ id: user._id }, SECRET_KEY, { expiresIn: '365d' });
            return { status: true, message: 'login successfully', data: token };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async getAppUserById(id) {
        try {
            const [userDetails] = await AppUser.aggregate(getAppUserByIdQuery(id))
            if (!userDetails)
                throw new AppError(404, 'User not found');
            return { status: true, message: 'Request Completed successfully', data: userDetails };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async updateUser(userId, userData) {
        const user = await AppUser.findById(new mongoose.ObjectId(userId));
        if (!user) {
            throw new AppError(404, 'User not found');
        }

        const updatedUser = await User.findByIdAndUpdate(
            new mongoose.Types.ObjectId(userId),
            {
                name: userData.name || user.name,
                phone: userData.phone || user.phone,
                email: userData.email || user.email
            },
            { new: true }
        );

        return updatedUser;
    }

    static async deleteUser(userId) {
        try {
            const user = await AppUser.findById(new mongoose.Types.ObjectId(userId));
            if (!user) {
                throw new AppError(404, 'User not found');
            }

            const deletedUser = await AppUser.findByIdAndDelete(new mongoose.Types.ObjectId(userId));
            return {
                status: true,
                message: 'User deleted Successfully',
                data: deletedUser
            };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async creatUserBySuperAdmin(userData, superAdminId) {
        try {
            const {
                firstName,
                lastName,
                email,
                // password,
                phone,
                locations = [],
                description,
                globalPermission,
                customPermissions = [],
                conciergeSpecialist
            } = userData;

            const existingUser = await AppUser.findOne({ email });
            if (existingUser) {
                throw new AppError(409, 'User already exists');
            }

            //const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, 10);
            const timestamp = Date.now();
            const password = `${firstName}_${lastName}_${timestamp}`;
            const hashedPassword = await bcrypt.hash(password, 10);
            const newUser = await AppUser.create({
                firstName,
                lastName,
                email,
                password: hashedPassword,
                phone,
                locations: locations.map(loc => new mongoose.Types.ObjectId(loc)),
                description,
                isEnable: true,
                conciergeSpecialist
            });

            const permissions = [];
            const customLocationIds = new Set(customPermissions.map(loc => loc.locationId.toString()));
            const allLocationIds = locations
                .map(id => new mongoose.Types.ObjectId(id))
                .filter(id => !customLocationIds.has(id.toString()));
            customPermissions.forEach(loc => {
                permissions.push({
                    superAdminId,
                    userId: newUser._id,
                    locationId: new mongoose.Types.ObjectId(loc.locationId),
                    permissions: loc.permissions,
                    isGlobal: false
                });
            });

            allLocationIds.forEach(locationId => {
                if (!customLocationIds.has(locationId.toString())) {
                    permissions.push({
                        superAdminId,
                        userId: newUser._id,
                        locationId,
                        permissions: globalPermission
                    });
                }
            });

            if (permissions.length) {
                await Permission.insertMany(permissions);
            }

            const emailData = {
                to: [email],
                bcc: [],
                content: {
                    subject: 'Your Account Has Been Created',
                    html: `
                    <p>Hi ${firstName},</p>
                    <p>Your account has been created by the admin.</p>
                    <p><strong>Login Email:</strong> ${email}</p>
                    <p><strong>Temporary Password:</strong> ${password}</p>
                    <p>Please log in and update your password immediately.</p>
                    <p>Thank you.</p>`
                }
            };
            await emailSender(emailData);

            return {
                message: 'User created successfully',
                user: newUser,
                status: true
            };

        } catch (err) {
            throw new AppError(err);
        }
    }

    static async updateUserBySuperAdmin(userId, userData, superAdminId) {
        try {
            const {
                firstName,
                lastName,
                email,
                phone,
                locations = [],
                description,
                globalPermission,
                customPermissions = []
            } = userData;

            const existingUser = await AppUser.findById(new mongoose.Types.ObjectId(userId));
            if (!existingUser) {
                throw new AppError(404, 'User not found');
            }

            existingUser.firstName = firstName;
            existingUser.lastName = lastName;
            existingUser.email = email;
            existingUser.phone = phone;
            existingUser.description = description;
            if (userId.toString() === superAdminId.toString()) {
                await existingUser.save();
                const data = await AppUser.aggregate(getAppUserByIdQuery(existingUser._id))
                return {
                    message: 'Your personal details were updated. You cannot update your own locations or permissions.',
                    user: data,
                    status: true
                };
            }

            existingUser.locations = locations.map(loc => new mongoose.Types.ObjectId(loc));
            await existingUser.save();

            await Permission.deleteMany({ userId: existingUser._id });

            const permissions = [];
            const customLocationIds = new Set(customPermissions.map(loc => loc.locationId.toString()));
            const allLocationIds = locations
                .map(id => new mongoose.Types.ObjectId(id))
                .filter(id => !customLocationIds.has(id.toString()));

            customPermissions.forEach(loc => {
                permissions.push({
                    superAdminId,
                    userId: existingUser._id,
                    locationId: new mongoose.Types.ObjectId(loc.locationId),
                    permissions: loc.permissions
                });
            });

            allLocationIds.forEach(locationId => {
                permissions.push({
                    superAdminId,
                    userId: existingUser._id,
                    locationId,
                    permissions: globalPermission
                });
            });

            if (permissions.length) {
                await Permission.insertMany(permissions);
            }

            const data = await AppUser.aggregate(getAppUserByIdQuery(existingUser._id))

            return {
                message: 'User updated successfully',
                user: data,
                status: true
            };

        } catch (err) {
            throw new AppError(err);
        }
    }

    static async getAllAppUsers(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';
            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getAllUsers(params, userLocations),
                params,
                userSearchFeilds,
                {},
            );
            const [users, count] = await Promise.all([
                AppUser.aggregate(query),
                AppUser.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: users,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async getDropdown(id) {
        try {
            const [existingUser] = await AppUser.aggregate([
                {
                    $match: {
                        _id: new mongoose.Types.ObjectId(id)
                    }
                },
                {
                    $lookup: {
                        from: 'permissions',
                        let: {
                            locationsIds: '$locations',
                            userId: new mongoose.Types.ObjectId(id)
                        },
                        as: 'permissions',
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$userId', '$$userId'] },
                                            { $eq: ['$isDeleted', false] },
                                            { $in: ['$locationId', '$$locationsIds'] }
                                        ]
                                    }
                                }
                            }
                        ]
                    }
                },
                {
                    $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        email: 1,
                        phone: 1,
                        locations: 1,
                        permissions: {
                            _id: 1,
                            locationId: 1,
                            permissions: 1,
                        }
                    }
                }
            ]
            );
            if (!existingUser) {
                throw new AppError(404, 'User Not founds');
            }
            const sources = await Source.aggregate([{ $project: { _id: 1, sourceName: 1 } }])
            const locations = await Location.aggregate([
                {
                    $match: {
                        _id: {
                            $in: existingUser.locations
                        },
                    }
                },
                {
                    $project: {
                        _id: 1,
                        location: 1,
                        meevo_location_id: 1,
                        meevo_tid: 1
                    }
                }
            ])
            const dropdown = {
                user: existingUser,
                sources,
                locations,
            }
            return { data: dropdown, status: true, mesage: 'Request completed successfully' };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async requestResetPassword(data) {
        try {
            const { email } = data
            const user = await AppUser.findOne({ email })
            if (!user) throw new AppError(404, 'User doesnot exist')
            const resetToken = crypto.randomBytes(32).toString('hex');
            const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

            user.resetPasswordToken = hashedToken;
            user.resetPasswordExpires = new Date(Date.now() + 3600000);
            await user.save();

            const encryptedEmail = encodeURIComponent(encryptEmail(email));
            const PROFILE = config.get('ENCRYPTION.PROFILE');
            const URL = config.get('ENCRYPTION.URL')
            const resetURL = `${URL}/${PROFILE}/reset-password?token=${resetToken}&email=${encryptedEmail}`;

            const emailData = {
                to: [email],
                bcc: [],
                content: {
                    subject: 'Reset Your Password',
                    html: `
            <p>Hi ${user.firstName || 'there'} ${user.lastName || ''},</p>
            <p>We received a request to reset your password for your account.</p>
            <p>Please click the button below to set a new password. This link will expire in <strong>30 minutes</strong>.</p>
            <p style="text-align: center; margin: 24px 0;">
                <a href="${resetURL}" target="_blank" style="background-color: #2d9cdb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                    Reset Password
                </a>
            </p>
            <p>If you didn't request a password reset, please ignore this email.</p>
            <p>Thank you!</p>
        `
                }
            };

            await emailSender(emailData);

            return {
                status: true,
                message: 'Reset link sent to email.',
                data: null
            };
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async resetPassword(data) {
        try {
            const { token, encryptedEmail, newPassword } = data
            const email = decryptEmail(decodeURIComponent(encryptedEmail));
            const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

            const user = await AppUser.findOne({
                email,
                resetPasswordToken: hashedToken,
                resetPasswordExpires: { $gt: Date.now() }
            });

            if (!user) throw new AppError(403, 'Link is expired.');

            user.password = await bcrypt.hash(newPassword, 10);
            user.resetPasswordToken = '';
            user.resetPasswordExpires = '';
            await user.save();

            return {
                status: true,
                message: 'Password changed successfully.',
                data: null
            };
        } catch (error) {
            handleServiceError(error)
        }
    }
}

module.exports = UserService;
