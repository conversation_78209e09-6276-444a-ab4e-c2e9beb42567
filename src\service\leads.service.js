const { baseListQuery } = require('../query/baseList.query');
const Lead = require('../model/lead');
const { AppError, generateCSV } = require('../util/index');
const { getLeadsQuery, getLeadsQueryOptimized, getLeadsQueryLightweight, getLeadsCountQuery, leadSearchFields } = require('../query/leads.query');
const AppUser = require('../model/appUser');
const { default: mongoose } = require('mongoose');
const handleServiceError = require('../util/handleError');
const Source = require('../model/source');
const Location = require('../model/location');
class LeadsService {
    static async getLeads(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';

            // Parse pagination parameters
            const pageNum = params.pageNum ? parseInt(params.pageNum) : 1;
            const limit = params.pageLimit ? parseInt(params.pageLimit) : 10;
            const skip = limit * (pageNum - 1);

            // Choose query based on performance requirements
            // Use lightweight query if call history is not needed (fastest)
            // Use optimized query for full data with better performance than original
            const useLight = params.lightweight === 'true' || params.lightweight === true;
            const dataQuery = useLight
                ? getLeadsQueryLightweight(params, userLocations, skip, limit)
                : getLeadsQueryOptimized(params, userLocations, skip, limit);
            const countQuery = getLeadsCountQuery(params, userLocations);

            // Execute both queries in parallel
            const [leads, count] = await Promise.all([
                Lead.aggregate(dataQuery),
                Lead.aggregate(countQuery),
            ]);

            const totalCount = count[0]?.totalCount || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';

            return {
                status,
                data: leads,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    static async exportLeadData(
        queryParams,
        loc
    ) {
        try {
            const { data } = await this.getLeads(queryParams, loc);
            const xlsxData = data.map((item) => ({
                Id: item._id || '',
                firstName: item.firstname || '',
                lastName: item.lastname || '',
                email: item.email || '',
                phone: item.phone || '',
                tagId: item.tag_id.join(',') || '',
                location: item.location.location || '',
                source: item.source[0]?.sourceName || '',
                service_status: item.service_status || '',
                createdAt: item.createdAt || ''
            }));
            const xlsxFields = [
                'Id',
                'firstName',
                'lastName',
                'email',
                'phone',
                'tagId',
                'location',
                'source',
                'service_status',
                'createdAt',
            ];
            const excelFile = await generateCSV(xlsxData, xlsxFields);

            return {
                status: true,
                message: 'request completed successfully',
                data: excelFile || [],
            }
        } catch (error) {
            handleServiceError(error)
        }
    }

    // static async createLead(
    //     data
    // ) {
    //     try {
    //         const sourceData = await Source.findOne({
    //             sourceName: 'website'
    //         });
    //         if (!sourceData) throw new AppError(404, 'Source Not found')
    //         const { user_id, firstname, lastname, email, phone, tag_id, location, position, source, gclid, UTM_campaign, UTM_medium, UTM_source,business_owner } = data;
    //         const locationData = await Location.findOne({ location });
    //         const leadData = {
    //             user_id,
    //             firstname,
    //             lastname,
    //             email,
    //             phone,
    //             tag_id,
    //             location,
    //             source: sourceData._id,
    //             position,
    //             location_Id: locationData?._id,
    //             business_owner: business_owner === true,
    //             UTM_details: {
    //                 UTM_source,
    //                 UTM_medium,
    //                 UTM_campaign,
    //                 gclid,
    //                 source
    //             }
    //         }

    //         const lead = await Lead.create(leadData)
    //         if (!lead)
    //             throw new AppError(403, 'Record not created');

    //         return {
    //             status: true,
    //             message: 'request completed successfully',
    //             data: lead,
    //         }
    //     } catch (error) {
    //         handleServiceError(error)
    //     }
    // }
    static async createLead(data) {
        try {
            const sourceData = await Source.findOne({
                sourceName: 'website'
            });
            if (!sourceData) throw new AppError(404, 'Source Not found');
    
            const {
                user_id,
                firstname,
                lastname,
                email,
                phone,
                tag_id,
                location_id, // numeric ID from payload
                position,
                source,
                gclid,
                UTM_campaign,
                UTM_medium,
                UTM_source,
                business_owner
            } = data;
    
           // Find location by meevo_location_id instead of location name
            const locationData = await Location.findOne({ meevo_location_id: String(location_id).trim() });
            if (!locationData) throw new AppError(404, 'Location Not found');
          
            
            const leadData = {
                user_id,
                firstname,
                lastname,
                email,
                phone,
                tag_id,
                location: locationData.location, // store human-readable name too if needed
                source: sourceData._id,
                position,
                location_Id: locationData._id, // store actual Mongo ObjectId
                business_owner: business_owner === true,
                UTM_details: {
                    UTM_source,
                    UTM_medium,
                    UTM_campaign,
                    gclid,
                    source
                }
            };
    
            const lead = await Lead.create(leadData);
            if (!lead) throw new AppError(403, 'Record not created');
    
            return {
                status: true,
                message: 'request completed successfully',
                data: lead,
            };
        } catch (error) {
            handleServiceError(error);
        }
    }
    
};

module.exports = LeadsService