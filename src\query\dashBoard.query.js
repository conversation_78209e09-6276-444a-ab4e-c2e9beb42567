const { default: mongoose } = require("mongoose");
const { LAST30DAYS, MTD, PREVMONTH, ALL } = require("../constant/enums");
const getLeadDashboardData = (params, userLocations) => [
    {
        $match: {
            ...(params.location_Id
                ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
                : userLocations?.length
                    ? {
                        location_Id: {
                            $in: userLocations.map(id => new mongoose.Types.ObjectId(id)),
                        },
                    }
                    : {}),

            ...(params.filter == LAST30DAYS
                ? {
                    createdAt: {
                        $gte: new Date(new Date().setDate(new Date().getDate() - 30)),
                        $lte: new Date(),
                    },
                }
                : {}),

            ...(params.filter == MTD
                ? {
                    createdAt: {
                        $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                        $lte: new Date(),
                    },
                }
                : {}),

            ...(params.filter == PREVMONTH
                ? (() => {
                    const start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                    const end = new Date(new Date().getFullYear(), new Date().getMonth(), 0, 23, 59, 59, 999);
                    return {
                        createdAt: {
                            $gte: start,
                            $lte: end,
                        },
                    };
                })()
                : {}),

            ...(params.filter == ALL ? {} : {}),
        }
    },
    {
        $facet: {
            data: [
                {
                    $group: {
                        _id: {
                            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" },
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $project: {
                        date: "$_id",
                        count: 1,
                        _id: 0,
                    },
                },
                { $sort: { date: 1 } }
            ],
            total: [
                { $count: "count" }
            ]
        }
    }
];

const getLeaderBoardQuery = (params, userLocations) => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfPrevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfPrevMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(now.getDate() - 7);

    return [
        {
            $match: {
                ...(params.location_Id
                    ? { _id: new mongoose.Types.ObjectId(params.location_Id) }
                    : userLocations?.length
                        ? {
                            _id: {
                                $in: userLocations.map((id) => new mongoose.Types.ObjectId(id)),
                            },
                        }
                        : {}),
            },
        },
        {
            $lookup: {
                from: "leads",
                localField: "_id",
                foreignField: "location_Id",
                as: "leads",
            },
        },
        { $unwind: { path: "$leads", preserveNullAndEmptyArrays: true } },
        {
            $lookup: {
                from: "callerhistories",
                localField: "leads._id",
                foreignField: "leadId",
                as: "leads.callerHistories",
            },
        },
        {
            $addFields: {
                "leads.called": {
                    $gt: [{ $size: "$leads.callerHistories" }, 0],
                },
            },
        },
        {
            $group: {
                _id: "$_id",
                location: { $first: "$location" },
                leads: { $push: "$leads" },
            },
        },
        {
            $project: {
                _id: 1,
                location: 1,
                bookedLeads: {
                    $size: {
                        $filter: {
                            input: "$leads",
                            as: "lead",
                            cond: {
                                $in: [
                                    "$$lead.service_status",
                                    ["BOOKED_3C", "BOOKED_5C", "BOOKED_7C", "BOOKED_OTHER"],
                                ],
                            },
                        },
                    },
                },
                mtdLeads: {
                    $size: {
                        $filter: {
                            input: "$leads",
                            as: "lead",
                            cond: {
                                $gte: ["$$lead.createdAt", startOfMonth],
                            },
                        },
                    },
                },
                prevMonthBookedLeads: {
                    $size: {
                        $filter: {
                            input: "$leads",
                            as: "lead",
                            cond: {
                                $and: [
                                    {
                                        $in: [
                                            "$$lead.service_status",
                                            ["BOOKED_3C", "BOOKED_5C", "BOOKED_7C", "BOOKED_OTHER"],
                                        ],
                                    },
                                    { $gte: ["$$lead.createdAt", startOfPrevMonth] },
                                    { $lt: ["$$lead.createdAt", endOfPrevMonth] },
                                ],
                            },
                        },
                    },
                },
                leadsCalledCount: {
                    $size: {
                        $filter: {
                            input: "$leads",
                            as: "lead",
                            cond: { $eq: ["$$lead.called", true] },
                        },
                    },
                },
                contactedLeads: {
                    $size: {
                        $filter: {
                            input: "$leads",
                            as: "lead",
                            cond: { $ne: ["$$lead.service_status", "NEEDS_ACTION"] },
                        },
                    },
                },
                // averageActionTime: {
                //     $cond: [
                //         { $gt: [{ $size: "$leads" }, 0] },
                //         {
                //             $divide: [
                //                 {
                //                     $sum: {
                //                         $map: {
                //                             input: "$leads",
                //                             as: "lead",
                //                             in: {
                //                                 $divide: [
                //                                     { $subtract: ["$$lead.updatedAt", "$$lead.createdAt"] },
                //                                     1000 * 60 // convert milliseconds to days
                //                                 ]
                //                             },
                //                         },
                //                     },
                //                 },
                //                 { $size: "$leads" },
                //             ],
                //         },
                //         0,
                //     ],
                // },
                averageActionTime: {
                    $cond: [
                      { $gt: [{ $size: "$leads" }, 0] },
                      {
                        $round: [
                          {
                            $divide: [
                              {
                                $sum: {
                                  $map: {
                                    input: "$leads",
                                    as: "lead",
                                    in: {
                                      $divide: [
                                        { $subtract: ["$$lead.updatedAt", "$$lead.createdAt"] },
                                        1000 * 60 
                                      ]
                                    },
                                  },
                                },
                              },
                              { $size: "$leads" },
                            ]
                          },
                          2 // 👈 rounds to 2 decimal places
                        ]
                      },
                      0,
                    ],
                  },
                  
                conversionRateLast7Days: {
                    $let: {
                        vars: {
                            last7Converted: {
                                $size: {
                                    $filter: {
                                        input: "$leads",
                                        as: "lead",
                                        cond: {
                                            $and: [
                                                { $ne: ["$$lead.meevo_client", null] },
                                                { $gte: ["$$lead.createdAt", sevenDaysAgo] },
                                            ],
                                        },
                                    },
                                },
                            },
                        },
                        in: {
                            $cond: [
                                { $gt: [{ $size: "$leads" }, 0] },
                                {
                                    $multiply: [
                                        {
                                            $divide: ["$$last7Converted", { $size: "$leads" }],
                                        },
                                        100,
                                    ],
                                },
                                0,
                            ],
                        },
                    },
                },
                conversionRatePrevMonth: {
                    $let: {
                        vars: {
                            prevMonthConverted: {
                                $size: {
                                    $filter: {
                                        input: "$leads",
                                        as: "lead",
                                        cond: {
                                            $and: [
                                                { $ne: ["$$lead.meevo_client", null] },
                                                { $gte: ["$$lead.createdAt", startOfPrevMonth] },
                                                { $lt: ["$$lead.createdAt", endOfPrevMonth] },
                                            ],
                                        },
                                    },
                                },
                            },
                            prevMonthTotal: {
                                $size: {
                                    $filter: {
                                        input: "$leads",
                                        as: "lead",
                                        cond: {
                                            $and: [
                                                { $gte: ["$$lead.createdAt", startOfPrevMonth] },
                                                { $lt: ["$$lead.createdAt", endOfPrevMonth] },
                                            ],
                                        },
                                    },
                                },
                            },
                        },
                        in: {
                            $cond: [
                                { $gt: ["$$prevMonthTotal", 0] },
                                {
                                    $multiply: [
                                        {
                                            $divide: ["$$prevMonthConverted", "$$prevMonthTotal"],
                                        },
                                        100,
                                    ],
                                },
                                0,
                            ],
                        },
                    },
                },
            },
        },
    ];
};

const getLeadBookingQuery = (params, userLocations) => [
  {
    $lookup: {
      from: "leads", // collection name of leads
      localField: "_id", // appuser._id
      foreignField: "callerId", // leads.callerId
      as: "userLeads"
    }
  },
  {
    $project: {
      firstName: 1,
      lastName: 1,
      email: 1,
      // total leads called
      leadCalled: { $size: "$userLeads" },

      // booked leads (all-time booked)
      leadBooked: {
        $size: {
          $filter: {
            input: "$userLeads",
            as: "lead",
            cond: {
              $regexMatch: {
                input: "$$lead.service_status",
                regex: "Booked",
                options: "i"
              }
            }
          }
        }
      },

      // prev month booked leads
      prevMonthBooked: {
        $size: {
          $filter: {
            input: "$userLeads",
            as: "lead",
            cond: {
              $and: [
                {
                  $regexMatch: {
                    input: "$$lead.service_status",
                    regex: "Booked",
                    options: "i"
                  }
                },
                {
                  $expr: {
                    $and: [
                      {
                        $eq: [
                          { $month: "$$lead.updatedAt" },
                          { $month: { $dateSubtract: { startDate: "$$NOW", unit: "month", amount: 1 } } }
                        ]
                      },
                      {
                        $eq: [
                          { $year: "$$lead.updatedAt" },
                          { $year: { $dateSubtract: { startDate: "$$NOW", unit: "month", amount: 1 } } }
                        ]
                      }
                    ]
                  }
                }
              ]
            }
          }
        }
      },

      // last 7 days booked leads
      last7daysBooked: {
        $size: {
          $filter: {
            input: "$userLeads",
            as: "lead",
            cond: {
              $and: [
                {
                  $regexMatch: {
                    input: "$$lead.service_status",
                    regex: "Booked",
                    options: "i"
                  }
                },
                {
                  $expr: {
                    $gte: [
                      "$$lead.updatedAt",
                      { $dateSubtract: { startDate: "$$NOW", unit: "day", amount: 7 } }
                    ]
                  }
                }
              ]
            }
          }
        }
      }
    }
  },
  {
    $addFields: {
      MTD: {
        $cond: [
          { $gt: ["$leadCalled", 0] }, // avoid division by zero
          {
            $concat: [
              {
                $toString: {
                  $round: [
                    { $multiply: [{ $divide: ["$leadBooked", "$leadCalled"] }, 100] },
                    2
                  ]
                }
              },
              "%"
            ]
          },
          "0%"
        ]
      }
    }
  }
];




const leaderBoardSearchFeilds = [
    'conversionRatePrevMonth',
    'conversionRateLast7Days',
    'prevMonthBookedLeads',
    'mtdLeads',
    'bookedLeads',
    'leadsCalledCount',
    'contactedLeads',
    'averageActionTime',
    'location',
];
module.exports = {
    getLeadDashboardData,
    getLeaderBoardQuery,
    leaderBoardSearchFeilds,
    getLeadBookingQuery
}