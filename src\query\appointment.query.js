const { default: mongoose } = require("mongoose");
const { ServiceStatus, combinedMap } = require("../constant/enums");

const getAppointmentsQuery = (params, userLocations) => [
    {
        $match: {
            ...(params.location_Id ? { location_id: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
                location_id: {
                    $in: userLocations.map(id => new mongoose.Types.ObjectId(id)),
                },
            }
                : {}),
            ...(params.appointment_type ? { book_type_txt: combinedMap[params.appointment_type] } : {}),
            ...(params.status_txt ? { status_txt: combinedMap[params.status_txt] } : {}),
            ...(params.dateFrom && params.dateEnd
                ? {
                    createdAt: {
                        $gte: new Date(params.dateFrom),
                        $lte: new Date(params.dateEnd),
                    }
                }
                : params.dateFrom
                    ? {
                        createdAt: {
                            $gte: new Date(params.dateFrom),
                        }
                    }
                    : {})
        }
    },
    {
        $lookup: {
            from: 'customers',
            localField: 'customer_id',
            foreignField: 'customer_id',
            as: 'lead'
        }
    },
    {
        $unwind: {
            path: '$lead',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $lookup: {
            from: 'sources',
            localField: 'lead.source',
            foreignField: '_id',
            as: 'source'
        }
    },
    {
        $unwind: {
            path: '$source',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $lookup: {
            from: 'locations',
            localField: 'location_id',
            foreignField: '_id',
            as: 'location'
        }
    },
    {
        $unwind: {
            path: '$location',
            preserveNullAndEmptyArrays: true
        }
    },
    ...(params.sourceId ? [{
        $match: {
            'source.sourceName': new mongoose.Types.ObjectId(params.sourceId)
        }
    }] : []),
    {
        $project: {
            _id: 1,
            appointment_id: 1,
            appointment_service_id: 1,
            book_method_txt: 1,
            book_type_txt: 1,
            firstName: '$lead.firstname',
            lastName: '$lead.lastname',
            email: '$lead.email',
            phone: '$lead.phone',
            status_txt: 1,
            created_on: 1,
            location: '$location.location',
            source: '$source.sourceName'
        }
    }
];

const appointmentSearchFeilds = [
    '_id',
    'appointment_id',
    'appointment_service_id',
    'book_method_txt',
    'book_type_txt',
    'firstName',
    'lastName',
    'email',
    'phone',
    'status_txt',
    'created_on',
    'location',
    'source'
];

module.exports = {
    getAppointmentsQuery,
    appointmentSearchFeilds
}