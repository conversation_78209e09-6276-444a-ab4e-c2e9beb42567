const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const meevoSaleSchema = new Schema({
    customer_id: { type: String },
    appointment_service_id: { type: String },
    cinvoiceno: { type: String, default: null },
    nprice: { type: mongoose.Types.Decimal128, required: true },
    nquantity: { type: Number, required: true },
    cservice: { type: String, default: null },
    iempid: { type: String, default: null },
    meevo_tenant: { type: String, required: true },
    meevo_location: { type: String, required: true },
    tdatetime: { type: Date, required: true },
    type: { type: String, required: true },
    guid: { type: String, required: true, unique: true },
    high: { type: Number, default: 0 },
    location_id: { type: mongoose.Types.ObjectId, ref: 'locations', required: true },
}, {
    timestamps: true
});

const MeevoSale = mongoose.model('meevoSale', meevoSaleSchema);

module.exports = MeevoSale;